"""
五子棋AI算法模块
实现高级AI算法：Minimax + Alpha-Beta剪枝 + 迭代加深 + VCF
"""

import time
import random
from gomoku_patterns import PatternRecognizer

class GomokuAI:
    def __init__(self, board_size=15, difficulty=3):
        self.board_size = board_size
        self.difficulty = difficulty
        self.pattern_recognizer = PatternRecognizer()

        # 搜索参数 - 调整为更保守的设置
        self.max_depth = min(difficulty + 1, 4)  # 最大搜索深度，更保守
        self.time_limit = 3.0  # 思考时间限制（秒），减少超时风险
        self.transposition_table = {}  # 置换表

        # VCF搜索参数
        self.vcf_depth = min(6, difficulty * 2)  # VCF搜索深度，更保守
        
    def get_best_move(self, board, player):
        """获取最佳落子位置"""
        start_time = time.time()
        
        # 清空置换表
        self.transposition_table.clear()
        
        # 1. 检查是否为开局
        if self.is_empty_board(board):
            center = self.board_size // 2
            return center, center
        
        # 2. 检查必胜手
        winning_move = self.find_winning_move(board, player)
        if winning_move:
            return winning_move
        
        # 3. 检查必防手
        defense_move = self.find_critical_defense(board, player)
        if defense_move:
            return defense_move
        
        # 4. VCF搜索（连续冲四）
        vcf_move = self.vcf_search(board, player)
        if vcf_move:
            return vcf_move
        
        # 5. 迭代加深搜索
        best_move = self.iterative_deepening_search(board, player, start_time)
        
        return best_move if best_move else self.get_random_move(board)
    
    def iterative_deepening_search(self, board, player, start_time):
        """迭代加深搜索"""
        best_move = None
        
        for depth in range(1, self.max_depth + 1):
            if time.time() - start_time > self.time_limit:
                break
            
            try:
                move, score = self.minimax_root(board, player, depth, start_time)
                if move:
                    best_move = move
                    
                # 如果找到必胜手，直接返回
                if score >= 900000:
                    break
                    
            except TimeoutError:
                break
        
        return best_move
    
    def minimax_root(self, board, player, depth, start_time):
        """Minimax搜索的根节点"""
        best_move = None
        best_score = float('-inf')
        alpha = float('-inf')
        beta = float('inf')
        
        candidates = self.get_candidates(board)
        
        # 对候选位置进行预排序
        candidates = self.sort_candidates(board, candidates, player)
        
        for x, y in candidates:
            if time.time() - start_time > self.time_limit:
                raise TimeoutError()
            
            if board[y][x] == 0:
                board[y][x] = player
                
                score = self.minimax(board, depth - 1, alpha, beta, False, player, start_time)
                
                board[y][x] = 0
                
                if score > best_score:
                    best_score = score
                    best_move = (x, y)
                
                alpha = max(alpha, score)
                if beta <= alpha:
                    break
        
        return best_move, best_score
    
    def minimax(self, board, depth, alpha, beta, maximizing, player, start_time):
        """Minimax算法与Alpha-Beta剪枝"""
        if time.time() - start_time > self.time_limit:
            raise TimeoutError()
        
        if depth == 0:
            return self.evaluate_board(board, player)
        
        # 检查置换表
        board_hash = self.hash_board(board)
        if board_hash in self.transposition_table:
            cached_depth, cached_score = self.transposition_table[board_hash]
            if cached_depth >= depth:
                return cached_score
        
        opponent = 3 - player
        current_player = player if maximizing else opponent
        
        candidates = self.get_candidates(board)
        candidates = self.sort_candidates(board, candidates, current_player)
        
        if maximizing:
            max_eval = float('-inf')
            for x, y in candidates:
                if board[y][x] == 0:
                    board[y][x] = player

                    try:
                        # 检查是否获胜
                        if self.check_five_in_row(board, x, y, player):
                            board[y][x] = 0
                            return 1000000

                        eval_score = self.minimax(board, depth - 1, alpha, beta, False, player, start_time)

                    except TimeoutError:
                        # 超时时确保恢复棋盘状态
                        board[y][x] = 0
                        raise

                    board[y][x] = 0

                    max_eval = max(max_eval, eval_score)
                    alpha = max(alpha, eval_score)

                    if beta <= alpha:
                        break

            self.transposition_table[board_hash] = (depth, max_eval)
            return max_eval
        else:
            min_eval = float('inf')
            for x, y in candidates:
                if board[y][x] == 0:
                    board[y][x] = opponent

                    try:
                        # 检查对手是否获胜
                        if self.check_five_in_row(board, x, y, opponent):
                            board[y][x] = 0
                            return -1000000

                        eval_score = self.minimax(board, depth - 1, alpha, beta, True, player, start_time)

                    except TimeoutError:
                        # 超时时确保恢复棋盘状态
                        board[y][x] = 0
                        raise

                    board[y][x] = 0

                    min_eval = min(min_eval, eval_score)
                    beta = min(beta, eval_score)

                    if beta <= alpha:
                        break

            self.transposition_table[board_hash] = (depth, min_eval)
            return min_eval
    
    def vcf_search(self, board, player):
        """VCF（Victory by Continuous Four）搜索"""
        return self._vcf_recursive(board, player, self.vcf_depth, True)
    
    def _vcf_recursive(self, board, player, depth, is_attacking):
        """VCF递归搜索"""
        if depth <= 0:
            return None
        
        if is_attacking:
            # 攻击方：寻找冲四手
            four_moves = self.find_four_moves(board, player)
            
            for x, y in four_moves:
                board[y][x] = player
                
                # 检查是否直接获胜
                if self.pattern_recognizer.is_winning_move(board, x, y, player):
                    board[y][x] = 0
                    return (x, y)
                
                # 递归搜索防守方的应对
                defense_successful = False
                defense_moves = self.find_defense_moves(board, 3 - player, x, y)
                
                for dx, dy in defense_moves:
                    board[dy][dx] = 3 - player
                    
                    # 递归搜索攻击方的下一步
                    result = self._vcf_recursive(board, player, depth - 1, True)
                    
                    board[dy][dx] = 0
                    
                    if result is None:  # 防守成功
                        defense_successful = True
                        break
                
                board[y][x] = 0
                
                if not defense_successful:  # 防不住，找到VCF
                    return (x, y)
        
        return None
    
    def find_four_moves(self, board, player):
        """寻找所有冲四手"""
        four_moves = []
        
        for y in range(self.board_size):
            for x in range(self.board_size):
                if board[y][x] == 0:
                    patterns = self.pattern_recognizer.analyze_position(board, x, y, player)
                    if any(p in ['FOUR', 'OPEN_FOUR'] for p in patterns):
                        four_moves.append((x, y))
        
        return four_moves
    
    def find_defense_moves(self, board, player, attack_x, attack_y):
        """寻找防守冲四的位置"""
        defense_moves = []
        
        # 检查攻击位置周围的防守点
        for dy in range(-1, 2):
            for dx in range(-1, 2):
                if dx == 0 and dy == 0:
                    continue
                
                x, y = attack_x + dx, attack_y + dy
                if (0 <= x < self.board_size and 0 <= y < self.board_size and 
                    board[y][x] == 0):
                    defense_moves.append((x, y))
        
        return defense_moves
    
    def evaluate_board(self, board, player):
        """评估棋盘局面"""
        my_score = self.evaluate_player(board, player)
        opponent_score = self.evaluate_player(board, 3 - player)
        
        # 防守权重更高
        return my_score - opponent_score * 1.1
    
    def evaluate_player(self, board, player):
        """评估某个玩家的局面分值"""
        total_score = 0
        
        for y in range(self.board_size):
            for x in range(self.board_size):
                if board[y][x] == player:
                    score = self.pattern_recognizer.evaluate_position(board, x, y, player)
                    total_score += score
        
        return total_score
    
    def get_candidates(self, board):
        """获取候选落子位置"""
        candidates = []
        
        for y in range(self.board_size):
            for x in range(self.board_size):
                if board[y][x] == 0 and self.has_neighbor(board, x, y, 2):
                    candidates.append((x, y))
        
        return candidates
    
    def sort_candidates(self, board, candidates, player):
        """对候选位置进行排序"""
        scored_candidates = []
        
        for x, y in candidates:
            score = self.pattern_recognizer.evaluate_position(board, x, y, player)
            scored_candidates.append((x, y, score))
        
        # 按分数降序排序
        scored_candidates.sort(key=lambda c: c[2], reverse=True)
        
        # 只返回前N个最佳候选
        max_candidates = min(20, len(scored_candidates))
        return [(x, y) for x, y, _ in scored_candidates[:max_candidates]]
    
    def has_neighbor(self, board, x, y, radius=1):
        """检查位置周围是否有棋子"""
        for dy in range(-radius, radius + 1):
            for dx in range(-radius, radius + 1):
                if dx == 0 and dy == 0:
                    continue
                
                nx, ny = x + dx, y + dy
                if (0 <= nx < self.board_size and 0 <= ny < self.board_size and 
                    board[ny][nx] != 0):
                    return True
        
        return False
    
    def find_winning_move(self, board, player):
        """寻找必胜手"""
        for y in range(self.board_size):
            for x in range(self.board_size):
                if board[y][x] == 0:
                    if self.pattern_recognizer.is_winning_move(board, x, y, player):
                        return (x, y)
        return None
    
    def find_critical_defense(self, board, player):
        """寻找必防手"""
        opponent = 3 - player

        # 1. 检查对手的必胜手（五连）
        winning_move = self.find_winning_move(board, opponent)
        if winning_move:
            return winning_move

        # 2. 检查对手的威胁手（四连、活三等）
        threat_move = self.find_threat_defense(board, opponent)
        if threat_move:
            return threat_move

        return None

    def find_threat_defense(self, board, opponent):
        """寻找威胁防守位置"""
        threats = []

        # 检查所有空位置，看对手在那里落子会形成什么威胁
        for y in range(self.board_size):
            for x in range(self.board_size):
                if board[y][x] == 0:
                    # 模拟对手在此位置落子
                    board[y][x] = opponent
                    patterns = self.pattern_recognizer.analyze_position(board, x, y, opponent)
                    board[y][x] = 0  # 恢复

                    # 评估威胁等级
                    threat_level = self.evaluate_threat_level(patterns)
                    if threat_level > 0:
                        threats.append((x, y, threat_level))

        # 按威胁等级排序，返回最高威胁的防守位置
        if threats:
            threats.sort(key=lambda t: t[2], reverse=True)
            return (threats[0][0], threats[0][1])

        return None

    def evaluate_threat_level(self, patterns):
        """评估威胁等级"""
        max_threat = 0

        for pattern in patterns:
            if pattern == 'FIVE':
                return 1000000  # 最高威胁
            elif pattern == 'OPEN_FOUR':
                max_threat = max(max_threat, 100000)
            elif pattern == 'FOUR':
                max_threat = max(max_threat, 10000)
            elif pattern == 'OPEN_THREE':
                max_threat = max(max_threat, 1000)
            elif pattern == 'SLEEP_THREE':
                max_threat = max(max_threat, 100)
            elif pattern == 'OPEN_TWO':
                max_threat = max(max_threat, 50)

        return max_threat
    
    def is_empty_board(self, board):
        """检查棋盘是否为空"""
        for row in board:
            if any(cell != 0 for cell in row):
                return False
        return True
    
    def is_game_over(self, board, player):
        """检查游戏是否结束"""
        # 简化版本，只检查是否有五连
        for y in range(self.board_size):
            for x in range(self.board_size):
                if board[y][x] == player:
                    if self.check_five_in_row(board, x, y, player):
                        return True
        return False
    
    def check_five_in_row(self, board, x, y, player):
        """检查是否有五连"""
        directions = [(1, 0), (0, 1), (1, 1), (1, -1)]
        
        for dx, dy in directions:
            count = 1
            
            # 正方向
            nx, ny = x + dx, y + dy
            while (0 <= nx < self.board_size and 0 <= ny < self.board_size and 
                   board[ny][nx] == player):
                count += 1
                nx += dx
                ny += dy
            
            # 反方向
            nx, ny = x - dx, y - dy
            while (0 <= nx < self.board_size and 0 <= ny < self.board_size and 
                   board[ny][nx] == player):
                count += 1
                nx -= dx
                ny -= dy
            
            if count >= 5:
                return True
        
        return False
    
    def get_random_move(self, board):
        """获取随机落子位置（备用方案）"""
        candidates = self.get_candidates(board)
        return random.choice(candidates) if candidates else (self.board_size // 2, self.board_size // 2)
    
    def hash_board(self, board):
        """计算棋盘哈希值"""
        return hash(tuple(tuple(row) for row in board))
