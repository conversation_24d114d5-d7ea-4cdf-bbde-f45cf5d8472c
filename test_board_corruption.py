"""
测试棋盘状态是否被意外修改
"""

from gomoku_ai import GomokuAI
from gomoku_patterns import PatternRecognizer
import copy

def print_board(board, title=""):
    """打印棋盘"""
    if title:
        print(f"{title}:")
    print("   ", end="")
    for i in range(15):
        print(f"{i:2}", end="")
    print()
    
    for i, row in enumerate(board):
        print(f"{i:2} ", end="")
        for cell in row:
            if cell == 0:
                print(" .", end="")
            elif cell == 1:
                print(" ●", end="")
            else:
                print(" ○", end="")
        print()
    print()

def test_pattern_recognizer():
    """测试模式识别器是否修改棋盘"""
    print("=== 测试模式识别器 ===")
    
    recognizer = PatternRecognizer()
    
    # 创建简单棋盘
    board = [[0 for _ in range(15)] for _ in range(15)]
    board[7][7] = 1  # 黑棋
    board[7][8] = 2  # 白棋
    
    # 保存原始状态
    original_board = copy.deepcopy(board)
    
    print_board(board, "原始棋盘")
    
    # 测试evaluate_position
    print("测试evaluate_position...")
    score = recognizer.evaluate_position(board, 6, 7, 2)
    print(f"评估分数: {score}")
    
    print_board(board, "evaluate_position后的棋盘")
    
    # 检查是否相同
    if board == original_board:
        print("✓ evaluate_position没有修改棋盘")
    else:
        print("✗ evaluate_position修改了棋盘!")
        return False
    
    # 测试is_winning_move
    print("测试is_winning_move...")
    is_win = recognizer.is_winning_move(board, 6, 7, 2)
    print(f"是否获胜: {is_win}")
    
    print_board(board, "is_winning_move后的棋盘")
    
    # 检查是否相同
    if board == original_board:
        print("✓ is_winning_move没有修改棋盘")
    else:
        print("✗ is_winning_move修改了棋盘!")
        return False
    
    return True

def test_ai_get_candidates():
    """测试AI获取候选位置是否修改棋盘"""
    print("=== 测试AI获取候选位置 ===")
    
    ai = GomokuAI(15, 3)
    
    # 创建简单棋盘
    board = [[0 for _ in range(15)] for _ in range(15)]
    board[7][7] = 1  # 黑棋
    board[7][8] = 2  # 白棋
    
    # 保存原始状态
    original_board = copy.deepcopy(board)
    
    print_board(board, "原始棋盘")
    
    # 测试get_candidates
    print("测试get_candidates...")
    candidates = ai.get_candidates(board)
    print(f"候选位置数量: {len(candidates)}")
    
    print_board(board, "get_candidates后的棋盘")
    
    # 检查是否相同
    if board == original_board:
        print("✓ get_candidates没有修改棋盘")
    else:
        print("✗ get_candidates修改了棋盘!")
        return False
    
    return True

def test_ai_sort_candidates():
    """测试AI排序候选位置是否修改棋盘"""
    print("=== 测试AI排序候选位置 ===")
    
    ai = GomokuAI(15, 3)
    
    # 创建简单棋盘
    board = [[0 for _ in range(15)] for _ in range(15)]
    board[7][7] = 1  # 黑棋
    board[7][8] = 2  # 白棋
    
    # 保存原始状态
    original_board = copy.deepcopy(board)
    
    print_board(board, "原始棋盘")
    
    # 获取候选位置
    candidates = ai.get_candidates(board)
    print(f"候选位置: {candidates[:5]}...")  # 只显示前5个
    
    # 测试sort_candidates
    print("测试sort_candidates...")
    sorted_candidates = ai.sort_candidates(board, candidates, 2)
    print(f"排序后候选位置: {sorted_candidates[:5]}...")  # 只显示前5个
    
    print_board(board, "sort_candidates后的棋盘")
    
    # 检查是否相同
    if board == original_board:
        print("✓ sort_candidates没有修改棋盘")
    else:
        print("✗ sort_candidates修改了棋盘!")
        return False
    
    return True

def test_ai_evaluate_board():
    """测试AI评估棋盘是否修改棋盘"""
    print("=== 测试AI评估棋盘 ===")
    
    ai = GomokuAI(15, 3)
    
    # 创建简单棋盘
    board = [[0 for _ in range(15)] for _ in range(15)]
    board[7][7] = 1  # 黑棋
    board[7][8] = 2  # 白棋
    
    # 保存原始状态
    original_board = copy.deepcopy(board)
    
    print_board(board, "原始棋盘")
    
    # 测试evaluate_board
    print("测试evaluate_board...")
    score = ai.evaluate_board(board, 2)
    print(f"评估分数: {score}")
    
    print_board(board, "evaluate_board后的棋盘")
    
    # 检查是否相同
    if board == original_board:
        print("✓ evaluate_board没有修改棋盘")
    else:
        print("✗ evaluate_board修改了棋盘!")
        return False
    
    return True

def main():
    """主测试函数"""
    print("测试棋盘状态是否被意外修改")
    print("=" * 50)
    
    tests = [
        ("模式识别器", test_pattern_recognizer),
        ("AI获取候选位置", test_ai_get_candidates),
        ("AI排序候选位置", test_ai_sort_candidates),
        ("AI评估棋盘", test_ai_evaluate_board),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 出错: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！棋盘状态没有被意外修改！")
    else:
        print("⚠️  部分测试失败，存在棋盘状态被修改的问题")

if __name__ == "__main__":
    main()
