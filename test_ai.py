"""
测试AI功能的脚本
"""

from gomoku_ai import GomokuAI
from gomoku_patterns import PatternRecognizer
import time

def print_board(board):
    """打印棋盘"""
    print("   ", end="")
    for i in range(15):
        print(f"{i:2}", end="")
    print()
    
    for i, row in enumerate(board):
        print(f"{i:2} ", end="")
        for cell in row:
            if cell == 0:
                print(" .", end="")
            elif cell == 1:
                print(" ●", end="")
            else:
                print(" ○", end="")
        print()
    print()

def test_ai_basic():
    """测试AI基本功能"""
    print("=== 测试AI基本功能 ===")
    
    # 创建AI
    ai = GomokuAI(15, 3)
    
    # 创建测试棋盘
    board = [[0 for _ in range(15)] for _ in range(15)]
    
    # 测试开局
    print("测试开局...")
    start_time = time.time()
    x, y = ai.get_best_move(board, 1)
    end_time = time.time()
    
    print(f"AI选择位置: ({x}, {y})")
    print(f"思考时间: {end_time - start_time:.3f}秒")
    
    # 放置第一个棋子
    board[y][x] = 1
    print_board(board)
    
    # 测试AI应对
    print("测试AI应对...")
    start_time = time.time()
    x2, y2 = ai.get_best_move(board, 2)
    end_time = time.time()
    
    print(f"AI应对位置: ({x2}, {y2})")
    print(f"思考时间: {end_time - start_time:.3f}秒")
    
    board[y2][x2] = 2
    print_board(board)

def test_pattern_recognition():
    """测试棋型识别"""
    print("=== 测试棋型识别 ===")
    
    recognizer = PatternRecognizer()
    
    # 创建测试棋盘
    board = [[0 for _ in range(15)] for _ in range(15)]
    
    # 测试活四
    board[7][5] = 1
    board[7][6] = 1
    board[7][7] = 1
    board[7][8] = 1
    
    print("测试活四识别:")
    print_board(board)
    
    patterns = recognizer.analyze_position(board, 4, 7, 1)  # 在左边放置
    print(f"在(4,7)放置黑棋的棋型: {patterns}")
    
    patterns = recognizer.analyze_position(board, 9, 7, 1)  # 在右边放置
    print(f"在(9,7)放置黑棋的棋型: {patterns}")
    
    # 测试必胜检测
    is_winning = recognizer.is_winning_move(board, 4, 7, 1)
    print(f"(4,7)是否为必胜手: {is_winning}")
    
    is_winning = recognizer.is_winning_move(board, 9, 7, 1)
    print(f"(9,7)是否为必胜手: {is_winning}")

def test_ai_defense():
    """测试AI防守能力"""
    print("=== 测试AI防守能力 ===")
    
    ai = GomokuAI(15, 4)
    
    # 创建危险局面
    board = [[0 for _ in range(15)] for _ in range(15)]
    
    # 黑棋形成四连威胁
    board[7][5] = 1
    board[7][6] = 1
    board[7][7] = 1
    board[7][8] = 1
    
    print("黑棋形成四连威胁:")
    print_board(board)
    
    # 测试AI是否能正确防守
    start_time = time.time()
    x, y = ai.get_best_move(board, 2)
    end_time = time.time()
    
    print(f"AI防守位置: ({x}, {y})")
    print(f"思考时间: {end_time - start_time:.3f}秒")
    
    # 检查是否正确防守
    if (x, y) == (4, 7) or (x, y) == (9, 7):
        print("✓ AI正确识别并防守了威胁!")
    else:
        print("✗ AI未能正确防守威胁")
    
    board[y][x] = 2
    print_board(board)

def test_ai_attack():
    """测试AI攻击能力"""
    print("=== 测试AI攻击能力 ===")
    
    ai = GomokuAI(15, 4)
    
    # 创建攻击机会
    board = [[0 for _ in range(15)] for _ in range(15)]
    
    # AI(白棋)有三连
    board[7][5] = 2
    board[7][6] = 2
    board[7][7] = 2
    
    print("AI有三连机会:")
    print_board(board)
    
    # 测试AI是否能抓住攻击机会
    start_time = time.time()
    x, y = ai.get_best_move(board, 2)
    end_time = time.time()
    
    print(f"AI攻击位置: ({x}, {y})")
    print(f"思考时间: {end_time - start_time:.3f}秒")
    
    # 检查是否形成四连
    if (x, y) == (4, 7) or (x, y) == (8, 7):
        print("✓ AI正确抓住了攻击机会!")
    else:
        print("? AI选择了其他策略")
    
    board[y][x] = 2
    print_board(board)

def main():
    """主测试函数"""
    print("五子棋AI测试程序")
    print("=" * 50)
    
    try:
        test_ai_basic()
        print()
        
        test_pattern_recognition()
        print()
        
        test_ai_defense()
        print()
        
        test_ai_attack()
        print()
        
        print("=" * 50)
        print("所有测试完成!")
        
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
