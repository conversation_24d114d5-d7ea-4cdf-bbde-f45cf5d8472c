"""
模拟真实游戏过程测试
"""

from gomoku_game import GomokuGame
import time

def print_board(board):
    """打印棋盘"""
    print("   ", end="")
    for i in range(15):
        print(f"{i:2}", end="")
    print()
    
    for i, row in enumerate(board):
        print(f"{i:2} ", end="")
        for cell in row:
            if cell == 0:
                print(" .", end="")
            elif cell == 1:
                print(" ●", end="")
            else:
                print(" ○", end="")
        print()
    print()

def simulate_game_moves():
    """模拟游戏步骤"""
    print("=== 模拟游戏过程 ===")
    
    # 创建游戏实例（但不启动GUI）
    game = GomokuGame()
    
    # 模拟一系列落子
    moves = [
        (7, 7, 1),   # 黑棋中心
        (7, 8, 2),   # 白棋应对
        (6, 7, 1),   # 黑棋
        (8, 7, 2),   # 白棋
        (8, 8, 1),   # 黑棋
        (6, 8, 2),   # 白棋
        (9, 9, 1),   # 黑棋
        (5, 7, 2),   # 白棋
        (5, 6, 1),   # 黑棋
    ]
    
    print("开始模拟游戏...")
    print_board(game.board)
    
    for step, (x, y, player) in enumerate(moves, 1):
        print(f"第{step}步: {'黑棋' if player == 1 else '白棋'}在({x},{y})落子")
        
        # 设置当前玩家
        game.current_player = player
        
        # 检查位置是否为空
        if game.board[y][x] != 0:
            print(f"错误：位置({x},{y})已有棋子!")
            continue
        
        # 放置棋子（不通过UI）
        game.board[y][x] = player
        game.move_history.append((x, y, player))
        game.last_move = (x, y)
        
        # 检查胜负
        if game.check_win(x, y, player):
            winner = "黑棋" if player == 1 else "白棋"
            print(f"*** {winner}获胜! ***")
            game.game_over = True
            print_board(game.board)
            return
        
        print_board(game.board)
        
        # 检查是否平局
        if game.is_board_full():
            print("*** 平局! ***")
            return
    
    print("游戏继续中...")

def test_specific_win_scenario():
    """测试特定的获胜场景"""
    print("=== 测试特定获胜场景 ===")
    
    game = GomokuGame()
    
    # 设置一个即将获胜的局面
    moves = [
        (7, 7, 1),   # 黑棋
        (6, 6, 1),   # 黑棋
        (8, 8, 1),   # 黑棋
        (9, 9, 1),   # 黑棋
    ]
    
    print("设置即将获胜的局面...")
    for x, y, player in moves:
        game.board[y][x] = player
    
    print_board(game.board)
    
    # 测试获胜手
    print("测试在(5,5)放黑棋是否获胜...")
    game.current_player = 1
    
    # 模拟place_piece的逻辑
    x, y = 5, 5
    game.board[y][x] = game.current_player
    game.move_history.append((x, y, game.current_player))
    game.last_move = (x, y)
    
    if game.check_win(x, y, game.current_player):
        winner = "黑棋" if game.current_player == 1 else "白棋"
        print(f"*** {winner}获胜! ***")
        game.game_over = True
    else:
        print("没有获胜")
    
    print_board(game.board)

def test_ai_vs_human_scenario():
    """测试AI对战场景"""
    print("=== 测试AI对战场景 ===")
    
    game = GomokuGame()
    
    # 设置一个AI需要防守的局面
    print("设置人类即将获胜的局面，测试AI是否能正确防守...")
    
    # 人类（黑棋）形成四连威胁
    human_moves = [
        (5, 7, 1),
        (6, 7, 1),
        (7, 7, 1),
        (8, 7, 1),
    ]
    
    for x, y, player in human_moves:
        game.board[y][x] = player
    
    print("人类形成四连威胁:")
    print_board(game.board)
    
    # 测试AI是否能找到正确的防守位置
    print("AI寻找防守位置...")
    ai_x, ai_y = game.ai.get_best_move(game.board, 2)
    
    print(f"AI选择位置: ({ai_x}, {ai_y})")
    
    # 检查AI的选择是否正确
    if (ai_x, ai_y) == (4, 7) or (ai_x, ai_y) == (9, 7):
        print("✓ AI正确识别并选择了防守位置!")
    else:
        print("? AI选择了其他策略")
    
    # 模拟AI落子
    game.current_player = 2
    game.board[ai_y][ai_x] = 2
    
    print("AI防守后的棋盘:")
    print_board(game.board)
    
    # 验证人类是否还能直接获胜
    can_win_left = game.check_win(4, 7, 1) if (ai_x, ai_y) != (4, 7) else False
    can_win_right = game.check_win(9, 7, 1) if (ai_x, ai_y) != (9, 7) else False
    
    if can_win_left or can_win_right:
        print("✗ 防守失败，人类仍可获胜")
    else:
        print("✓ 防守成功，人类无法直接获胜")

def main():
    """主测试函数"""
    print("五子棋游戏过程模拟测试")
    print("=" * 50)
    
    try:
        simulate_game_moves()
        print()
        
        test_specific_win_scenario()
        print()
        
        test_ai_vs_human_scenario()
        print()
        
        print("=" * 50)
        print("所有模拟测试完成!")
        
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
