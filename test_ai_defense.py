"""
测试AI防守逻辑
"""

from gomoku_game import <PERSON>mokuGame
from gomoku_patterns import PatternRecognizer

def print_board(board, title=""):
    """打印棋盘"""
    if title:
        print(f"{title}:")
    print("   ", end="")
    for i in range(15):
        print(f"{i:2}", end="")
    print()
    
    for i, row in enumerate(board):
        print(f"{i:2} ", end="")
        for cell in row:
            if cell == 0:
                print(" .", end="")
            elif cell == 1:
                print(" ●", end="")
            else:
                print(" ○", end="")
        print()
    print()

def test_three_in_row_defense():
    """测试对三连的防守"""
    print("=== 测试对三连的防守 ===")
    
    game = GomokuGame()
    
    # 创建人类（黑棋）三连的局面
    game.board[7][5] = 1  # 黑棋
    game.board[7][6] = 1  # 黑棋
    game.board[7][7] = 1  # 黑棋
    
    print_board(game.board, "人类有三连威胁")
    
    # 分析威胁位置
    recognizer = PatternRecognizer()
    
    # 检查左边位置(4,7)的威胁
    patterns_left = recognizer.analyze_position(game.board, 4, 7, 1)
    print(f"在(4,7)放黑棋的棋型: {patterns_left}")
    
    # 检查右边位置(8,7)的威胁
    patterns_right = recognizer.analyze_position(game.board, 8, 7, 1)
    print(f"在(8,7)放黑棋的棋型: {patterns_right}")
    
    # 测试AI是否能识别并防守
    print("\nAI分析...")
    ai_x, ai_y = game.ai.get_best_move(game.board, 2)
    
    print(f"AI选择位置: ({ai_x}, {ai_y})")
    
    # 检查AI是否选择了防守位置
    if (ai_x, ai_y) == (4, 7) or (ai_x, ai_y) == (8, 7):
        print("✓ AI正确选择了防守位置!")
        return True
    else:
        print("✗ AI没有选择防守位置，这是错误的!")
        
        # 显示AI选择位置后的棋盘
        game.board[ai_y][ai_x] = 2
        print_board(game.board, "AI选择后的棋盘")
        
        # 检查人类是否还能获胜
        can_win_left = recognizer.is_winning_move(game.board, 4, 7, 1)
        can_win_right = recognizer.is_winning_move(game.board, 8, 7, 1)
        
        if can_win_left or can_win_right:
            print("✗ 人类仍可以在下一步获胜!")
        
        return False

def test_open_three_defense():
    """测试对活三的防守"""
    print("=== 测试对活三的防守 ===")
    
    game = GomokuGame()
    
    # 创建活三局面 _XXX_
    game.board[7][6] = 1  # 黑棋
    game.board[7][7] = 1  # 黑棋
    game.board[7][8] = 1  # 黑棋
    # 位置(7,5)和(7,9)都是空的，形成活三
    
    print_board(game.board, "人类有活三威胁")
    
    # 分析威胁
    recognizer = PatternRecognizer()
    patterns_left = recognizer.analyze_position(game.board, 5, 7, 1)
    patterns_right = recognizer.analyze_position(game.board, 9, 7, 1)
    
    print(f"在(5,7)放黑棋的棋型: {patterns_left}")
    print(f"在(9,7)放黑棋的棋型: {patterns_right}")
    
    # 测试AI防守
    print("\nAI分析...")
    ai_x, ai_y = game.ai.get_best_move(game.board, 2)
    
    print(f"AI选择位置: ({ai_x}, {ai_y})")
    
    # AI应该选择防守位置之一
    if (ai_x, ai_y) == (5, 7) or (ai_x, ai_y) == (9, 7):
        print("✓ AI正确防守了活三!")
        return True
    else:
        print("✗ AI没有防守活三!")
        return False

def test_four_in_row_defense():
    """测试对四连的防守"""
    print("=== 测试对四连的防守 ===")
    
    game = GomokuGame()
    
    # 创建四连威胁
    game.board[7][5] = 1  # 黑棋
    game.board[7][6] = 1  # 黑棋
    game.board[7][7] = 1  # 黑棋
    game.board[7][8] = 1  # 黑棋
    
    print_board(game.board, "人类有四连威胁")
    
    # 测试AI防守
    print("AI分析...")
    ai_x, ai_y = game.ai.get_best_move(game.board, 2)
    
    print(f"AI选择位置: ({ai_x}, {ai_y})")
    
    # AI应该选择防守位置
    if (ai_x, ai_y) == (4, 7) or (ai_x, ai_y) == (9, 7):
        print("✓ AI正确防守了四连!")
        return True
    else:
        print("✗ AI没有防守四连!")
        return False

def test_ai_threat_detection():
    """测试AI威胁检测功能"""
    print("=== 测试AI威胁检测功能 ===")
    
    game = GomokuGame()
    recognizer = PatternRecognizer()
    
    # 创建三连
    game.board[7][5] = 1
    game.board[7][6] = 1
    game.board[7][7] = 1
    
    print_board(game.board, "测试棋盘")
    
    # 测试find_critical_defense
    defense_move = game.ai.find_critical_defense(game.board, 2)
    print(f"find_critical_defense结果: {defense_move}")
    
    # 测试威胁检测
    threats = recognizer.find_threats(game.board, 1)
    print(f"威胁检测结果: {threats}")
    
    # 手动检查各个位置的威胁等级
    test_positions = [(4, 7), (8, 7), (6, 8), (7, 4)]
    
    for x, y in test_positions:
        if game.board[y][x] == 0:
            patterns = recognizer.analyze_position(game.board, x, y, 1)
            score = recognizer.evaluate_position(game.board, x, y, 1)
            print(f"位置({x},{y}): 棋型{patterns}, 分数{score}")

def main():
    """主测试函数"""
    print("AI防守逻辑测试")
    print("=" * 50)
    
    tests = [
        ("AI威胁检测功能", test_ai_threat_detection),
        ("四连防守", test_four_in_row_defense),
        ("活三防守", test_open_three_defense),
        ("三连防守", test_three_in_row_defense),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 出错: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 AI防守逻辑完全正确!")
    else:
        print("⚠️  AI防守逻辑需要改进")

if __name__ == "__main__":
    main()
