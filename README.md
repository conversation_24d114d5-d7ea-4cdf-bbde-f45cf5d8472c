# 五子棋游戏 - 增强版

这是一个功能强大的五子棋游戏，具有智能AI对手和丰富的游戏功能。

## 主要特点

### 🤖 强大的AI对手
- **5个难度等级**: 从简单到大师级别
- **Minimax算法**: 使用Alpha-Beta剪枝优化
- **迭代加深搜索**: 在时间限制内找到最佳解
- **VCF算法**: Victory by Continuous Four (连续冲四)
- **精确棋型识别**: 识别活四、冲四、活三等复杂棋型
- **置换表缓存**: 提高搜索效率

### 🎮 游戏功能
- **人机对战**: 与AI对手对弈
- **人人对战**: 双人游戏模式
- **悔棋功能**: 支持Ctrl+Z快捷键悔棋
- **实时预览**: 鼠标悬停显示落子预览
- **最后一步标记**: 红色圆点标记最后下的棋子
- **游戏历史**: 显示最近的落子记录

### 🎨 用户界面
- **直观的棋盘**: 标准15x15棋盘，带星位标记
- **状态显示**: 实时显示当前回合和游戏状态
- **AI思考时间**: 显示AI的思考时间
- **信息面板**: 详细的游戏信息和操作提示
- **难度调节**: 可随时调整AI难度

## 文件结构

```
├── main.py              # 主程序入口
├── gomoku_game.py       # 游戏主类，UI和游戏逻辑
├── gomoku_ai.py         # AI算法模块
├── gomoku_patterns.py   # 棋型识别和评估模块
├── gomoku.py           # 原始版本（保留）
└── README.md           # 说明文档
```

## 运行方法

### 方法1: 运行增强版
```bash
python main.py
```

### 方法2: 运行原始版本
```bash
python gomoku.py
```

## 操作说明

### 基本操作
- **下棋**: 点击棋盘空位
- **悔棋**: 按Ctrl+Z或点击悔棋按钮
- **重新开始**: 点击重新开始按钮
- **切换模式**: 在人机对战和人人对战间切换

### AI难度说明
1. **简单**: 基础算法，适合初学者
2. **普通**: 中等搜索深度，有一定挑战性
3. **困难**: 较深搜索，能识别复杂棋型
4. **专家**: 高级算法，具有强攻击性
5. **大师**: 最高难度，使用全部算法特性

## 技术特点

### AI算法优化
- **Minimax + Alpha-Beta剪枝**: 经典的博弈树搜索算法
- **迭代加深**: 在时间限制内逐步加深搜索
- **候选位置筛选**: 只搜索有意义的位置
- **棋型评估**: 精确识别各种攻防棋型
- **VCF搜索**: 寻找连续冲四的必胜路径

### 棋型识别系统
- **五连**: 必胜棋型
- **活四**: 下一步必胜
- **冲四**: 单方向四连
- **活三**: 可形成活四的三连
- **眠三**: 被阻挡的三连
- **活二**: 可发展的二连
- **组合棋型**: 双活三、冲四活三等

### 性能优化
- **置换表**: 缓存已计算的局面评估
- **候选位置排序**: 优先搜索高价值位置
- **时间控制**: 限制AI思考时间
- **多线程**: AI计算不阻塞UI

## 与原版对比

| 特性 | 原版 | 增强版 |
|------|------|--------|
| AI算法 | 基础规则 | Minimax + Alpha-Beta |
| 搜索深度 | 固定浅层 | 迭代加深 |
| 棋型识别 | 简单 | 精确复杂 |
| VCF算法 | 无 | 有 |
| 悔棋功能 | 无 | 有 |
| 预览功能 | 无 | 有 |
| 信息显示 | 基础 | 详细 |
| 代码结构 | 单文件 | 模块化 |

## 系统要求

- Python 3.6+
- tkinter (通常随Python安装)
- 无需额外依赖

## 开发说明

这个增强版本采用模块化设计：

- `gomoku_patterns.py`: 负责棋型识别和评估
- `gomoku_ai.py`: 实现AI算法和搜索
- `gomoku_game.py`: 游戏逻辑和用户界面
- `main.py`: 程序入口和初始化

每个模块都有清晰的职责分工，便于维护和扩展。

## 未来改进方向

- [ ] 添加开局库
- [ ] 实现禁手规则
- [ ] 添加游戏保存/加载功能
- [ ] 网络对战功能
- [ ] 更多AI算法（如蒙特卡洛树搜索）
- [ ] 游戏回放功能

## 许可证

本项目采用MIT许可证，可自由使用和修改。
