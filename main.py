"""
五子棋游戏主程序
增强版五子棋，具有强大的AI对手
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gomoku_game import GomokuGame

def main():
    """主函数"""
    print("=" * 50)
    print("五子棋游戏 - 增强版")
    print("=" * 50)
    print("功能特点:")
    print("• 强大的AI对手 (5个难度等级)")
    print("• 使用Minimax算法 + Alpha-Beta剪枝")
    print("• VCF (连续冲四) 算法")
    print("• 精确的棋型识别")
    print("• 悔棋功能 (Ctrl+Z)")
    print("• 实时预览落子位置")
    print("• 详细的游戏信息显示")
    print("=" * 50)
    print("正在初始化游戏...")

    try:
        # 创建并运行游戏
        print("创建游戏实例...")
        game = GomokuGame()
        print("启动游戏界面...")
        game.run()

    except KeyboardInterrupt:
        print("\n游戏被用户中断")
    except Exception as e:
        print(f"游戏运行出错: {e}")
        import traceback
        traceback.print_exc()

    print("游戏结束，感谢游戏!")

if __name__ == "__main__":
    main()
