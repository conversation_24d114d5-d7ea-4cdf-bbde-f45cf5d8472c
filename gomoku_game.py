"""
五子棋游戏主类
优化的UI和游戏逻辑
"""

import tkinter as tk
from tkinter import messagebox, ttk
import threading
import time
from gomoku_ai import GomokuAI

class GomokuGame:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("五子棋 - 增强版")
        self.root.resizable(False, False)
        
        # 游戏常量
        self.BOARD_SIZE = 15
        self.CELL_SIZE = 40
        self.PIECE_RADIUS = 18
        
        # 游戏状态
        self.board = [[0 for _ in range(self.BOARD_SIZE)] for _ in range(self.BOARD_SIZE)]
        self.current_player = 1  # 1: 黑棋(人类), 2: 白棋(AI)
        self.game_over = False
        self.game_mode = 'pvc'  # 'pvp': 人人对战, 'pvc': 人机对战
        self.ai_player = 2
        self.ai_difficulty = 3  # 默认难度改为3，更稳定
        self.thinking = False
        
        # 游戏历史
        self.move_history = []
        self.last_move = None
        
        # 创建AI
        self.ai = GomokuAI(self.BOARD_SIZE, self.ai_difficulty)
        
        # 创建UI
        self.setup_ui()
        
        # 绑定键盘事件
        self.root.bind('<Control-z>', lambda e: self.undo_move())
        self.root.focus_set()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = tk.Frame(self.root)
        main_frame.pack(padx=10, pady=10)
        
        # 状态栏
        self.setup_status_bar(main_frame)
        
        # 控制面板
        self.setup_control_panel(main_frame)
        
        # 棋盘
        self.setup_board(main_frame)
        
        # 信息面板
        self.setup_info_panel(main_frame)
        
    def setup_status_bar(self, parent):
        """设置状态栏"""
        status_frame = tk.Frame(parent)
        status_frame.pack(pady=5)
        
        self.status_label = tk.Label(status_frame, text="黑棋先手", font=("Arial", 14, "bold"))
        self.status_label.pack()
        
        self.thinking_label = tk.Label(status_frame, text="", font=("Arial", 10), fg="blue")
        self.thinking_label.pack()
        
    def setup_control_panel(self, parent):
        """设置控制面板"""
        control_frame = tk.Frame(parent)
        control_frame.pack(pady=5)
        
        # 游戏模式
        tk.Button(control_frame, text="切换模式", command=self.toggle_mode).pack(side=tk.LEFT, padx=2)
        
        # AI难度
        difficulty_frame = tk.Frame(control_frame)
        difficulty_frame.pack(side=tk.LEFT, padx=5)
        
        tk.Label(difficulty_frame, text="AI难度:").pack(side=tk.LEFT)
        self.difficulty_var = tk.StringVar(value=str(self.ai_difficulty))
        difficulty_combo = ttk.Combobox(difficulty_frame, textvariable=self.difficulty_var, 
                                       values=['1', '2', '3', '4', '5'], width=3, state="readonly")
        difficulty_combo.pack(side=tk.LEFT, padx=2)
        difficulty_combo.bind('<<ComboboxSelected>>', self.change_difficulty)
        
        # 其他控制按钮
        tk.Button(control_frame, text="悔棋", command=self.undo_move).pack(side=tk.LEFT, padx=2)
        tk.Button(control_frame, text="重新开始", command=self.restart_game).pack(side=tk.LEFT, padx=2)
        tk.Button(control_frame, text="退出", command=self.root.quit).pack(side=tk.LEFT, padx=2)
        
    def setup_board(self, parent):
        """设置棋盘"""
        board_frame = tk.Frame(parent)
        board_frame.pack(pady=10)
        
        self.canvas_size = self.BOARD_SIZE * self.CELL_SIZE
        self.canvas = tk.Canvas(board_frame, width=self.canvas_size, height=self.canvas_size, 
                               bg="wheat", highlightthickness=2, highlightbackground="black")
        self.canvas.pack()
        self.canvas.bind("<Button-1>", self.on_click)
        self.canvas.bind("<Motion>", self.on_mouse_move)
        
        self.draw_board()
        
    def setup_info_panel(self, parent):
        """设置信息面板"""
        info_frame = tk.Frame(parent)
        info_frame.pack(pady=5)
        
        # 游戏信息
        self.info_text = tk.Text(info_frame, height=6, width=50, font=("Consolas", 9))
        self.info_text.pack(side=tk.LEFT)
        
        scrollbar = tk.Scrollbar(info_frame, orient="vertical", command=self.info_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill="y")
        self.info_text.config(yscrollcommand=scrollbar.set)
        
        self.update_info_panel()
        
    def draw_board(self):
        """绘制棋盘"""
        self.canvas.delete("all")
        
        # 绘制网格线
        for i in range(self.BOARD_SIZE):
            # 垂直线
            x = i * self.CELL_SIZE + self.CELL_SIZE // 2
            self.canvas.create_line(x, self.CELL_SIZE // 2, x, 
                                  self.canvas_size - self.CELL_SIZE // 2, fill="black", width=1)
            # 水平线
            y = i * self.CELL_SIZE + self.CELL_SIZE // 2
            self.canvas.create_line(self.CELL_SIZE // 2, y, 
                                  self.canvas_size - self.CELL_SIZE // 2, y, fill="black", width=1)
        
        # 绘制星位
        star_positions = [3, 7, 11]
        for i in star_positions:
            for j in star_positions:
                x = i * self.CELL_SIZE + self.CELL_SIZE // 2
                y = j * self.CELL_SIZE + self.CELL_SIZE // 2
                self.canvas.create_oval(x-3, y-3, x+3, y+3, fill="black")
        
        # 重新绘制所有棋子
        self.redraw_pieces()
        
    def redraw_pieces(self):
        """重新绘制所有棋子"""
        for y in range(self.BOARD_SIZE):
            for x in range(self.BOARD_SIZE):
                if self.board[y][x] != 0:
                    self.draw_piece(x, y, self.board[y][x])
        
        # 标记最后一步
        if self.last_move:
            self.mark_last_move(self.last_move[0], self.last_move[1])
    
    def draw_piece(self, x, y, player):
        """绘制棋子"""
        canvas_x = x * self.CELL_SIZE + self.CELL_SIZE // 2
        canvas_y = y * self.CELL_SIZE + self.CELL_SIZE // 2
        
        color = "black" if player == 1 else "white"
        outline_color = "white" if player == 1 else "black"
        
        self.canvas.create_oval(
            canvas_x - self.PIECE_RADIUS, canvas_y - self.PIECE_RADIUS,
            canvas_x + self.PIECE_RADIUS, canvas_y + self.PIECE_RADIUS,
            fill=color, outline=outline_color, width=2, tags=f"piece_{x}_{y}"
        )
    
    def mark_last_move(self, x, y):
        """标记最后一步"""
        canvas_x = x * self.CELL_SIZE + self.CELL_SIZE // 2
        canvas_y = y * self.CELL_SIZE + self.CELL_SIZE // 2
        
        # 绘制红色标记
        self.canvas.create_oval(
            canvas_x - 5, canvas_y - 5, canvas_x + 5, canvas_y + 5,
            fill="red", outline="red", tags="last_move"
        )
    
    def on_click(self, event):
        """处理鼠标点击"""
        if self.game_over or self.thinking:
            return
        
        # 计算点击位置对应的棋盘坐标
        x = event.x // self.CELL_SIZE
        y = event.y // self.CELL_SIZE
        
        if 0 <= x < self.BOARD_SIZE and 0 <= y < self.BOARD_SIZE:
            if self.board[y][x] == 0:  # 空位置
                self.place_piece(x, y)
    
    def on_mouse_move(self, event):
        """处理鼠标移动（显示预览）"""
        if self.game_over or self.thinking:
            return
        
        # 清除之前的预览
        self.canvas.delete("preview")
        
        # 计算鼠标位置对应的棋盘坐标
        x = event.x // self.CELL_SIZE
        y = event.y // self.CELL_SIZE
        
        if (0 <= x < self.BOARD_SIZE and 0 <= y < self.BOARD_SIZE and 
            self.board[y][x] == 0):
            
            canvas_x = x * self.CELL_SIZE + self.CELL_SIZE // 2
            canvas_y = y * self.CELL_SIZE + self.CELL_SIZE // 2
            
            color = "gray" if self.current_player == 1 else "lightgray"
            self.canvas.create_oval(
                canvas_x - self.PIECE_RADIUS, canvas_y - self.PIECE_RADIUS,
                canvas_x + self.PIECE_RADIUS, canvas_y + self.PIECE_RADIUS,
                fill=color, outline="gray", width=1, tags="preview"
            )
    
    def place_piece(self, x, y):
        """放置棋子"""
        # 清除预览
        self.canvas.delete("preview")
        self.canvas.delete("last_move")
        
        # 在棋盘数组中记录
        self.board[y][x] = self.current_player
        self.move_history.append((x, y, self.current_player))
        self.last_move = (x, y)
        
        # 绘制棋子
        self.draw_piece(x, y, self.current_player)
        self.mark_last_move(x, y)
        
        # 检查胜负
        if self.check_win(x, y, self.current_player):
            winner = "黑棋" if self.current_player == 1 else "白棋"
            self.status_label.config(text=f"{winner}获胜!")
            self.game_over = True
            messagebox.showinfo("游戏结束", f"{winner}获胜!")
            return
        
        # 检查平局
        if self.is_board_full():
            self.status_label.config(text="平局!")
            self.game_over = True
            messagebox.showinfo("游戏结束", "平局!")
            return
        
        # 切换玩家
        self.current_player = 3 - self.current_player
        self.update_status()
        self.update_info_panel()
        
        # 如果轮到AI且游戏未结束，让AI下棋
        if (self.current_player == self.ai_player and not self.game_over and 
            self.game_mode == 'pvc'):
            self.ai_move()
    
    def ai_move(self):
        """AI下棋"""
        self.thinking = True
        self.thinking_label.config(text="AI思考中...")
        self.root.update()
        
        # 在新线程中运行AI计算
        def ai_thread():
            start_time = time.time()
            x, y = self.ai.get_best_move(self.board, self.ai_player)
            think_time = time.time() - start_time
            
            # 在主线程中更新UI
            self.root.after(0, lambda: self.complete_ai_move(x, y, think_time))
        
        threading.Thread(target=ai_thread, daemon=True).start()
    
    def complete_ai_move(self, x, y, think_time):
        """完成AI落子"""
        self.thinking = False
        self.thinking_label.config(text=f"AI思考时间: {think_time:.2f}秒")
        
        if x is not None and y is not None:
            self.place_piece(x, y)
    
    def check_win(self, x, y, player=None):
        """检查胜负"""
        if player is None:
            player = self.current_player

        directions = [(1, 0), (0, 1), (1, 1), (1, -1)]

        for dx, dy in directions:
            count = 1

            # 正方向检查
            i, j = x + dx, y + dy
            while (0 <= i < self.BOARD_SIZE and 0 <= j < self.BOARD_SIZE and
                   self.board[j][i] == player):
                count += 1
                i += dx
                j += dy

            # 反方向检查
            i, j = x - dx, y - dy
            while (0 <= i < self.BOARD_SIZE and 0 <= j < self.BOARD_SIZE and
                   self.board[j][i] == player):
                count += 1
                i -= dx
                j -= dy

            if count >= 5:
                return True

        return False
    
    def is_board_full(self):
        """检查棋盘是否已满"""
        for row in self.board:
            if 0 in row:
                return False
        return True
    
    def undo_move(self):
        """悔棋"""
        if not self.move_history or self.thinking:
            return
        
        # 人机对战时，悔棋需要撤销两步（玩家和AI的）
        steps_to_undo = 2 if self.game_mode == 'pvc' and len(self.move_history) >= 2 else 1
        
        for _ in range(min(steps_to_undo, len(self.move_history))):
            x, y, player = self.move_history.pop()
            self.board[y][x] = 0
            self.canvas.delete(f"piece_{x}_{y}")
        
        # 更新最后一步标记
        self.canvas.delete("last_move")
        if self.move_history:
            last_x, last_y, _ = self.move_history[-1]
            self.last_move = (last_x, last_y)
            self.mark_last_move(last_x, last_y)
        else:
            self.last_move = None
        
        # 重置游戏状态
        self.game_over = False
        self.current_player = 1 if self.game_mode == 'pvp' else 1
        
        self.update_status()
        self.update_info_panel()
    
    def restart_game(self):
        """重新开始游戏"""
        self.board = [[0 for _ in range(self.BOARD_SIZE)] for _ in range(self.BOARD_SIZE)]
        self.current_player = 1
        self.game_over = False
        self.thinking = False
        self.move_history = []
        self.last_move = None
        
        self.draw_board()
        self.update_status()
        self.update_info_panel()
        self.thinking_label.config(text="")
    
    def toggle_mode(self):
        """切换游戏模式"""
        self.game_mode = 'pvc' if self.game_mode == 'pvp' else 'pvp'
        mode_text = "人机对战" if self.game_mode == 'pvc' else "人人对战"
        messagebox.showinfo("游戏模式", f"切换到{mode_text}模式")
        self.restart_game()
    
    def change_difficulty(self, event=None):
        """改变AI难度"""
        try:
            new_difficulty = int(self.difficulty_var.get())
            if 1 <= new_difficulty <= 5:
                self.ai_difficulty = new_difficulty
                self.ai = GomokuAI(self.BOARD_SIZE, self.ai_difficulty)
                difficulties = {1: "简单", 2: "普通", 3: "困难", 4: "专家", 5: "大师"}
                messagebox.showinfo("AI难度", f"难度设置为: {difficulties[new_difficulty]}")
        except ValueError:
            pass
    
    def update_status(self):
        """更新状态显示"""
        if self.game_over:
            return
        
        mode_text = "人机对战" if self.game_mode == 'pvc' else "人人对战"
        
        if self.thinking:
            self.status_label.config(text="AI思考中...")
        elif self.current_player == self.ai_player and self.game_mode == 'pvc':
            difficulties = {1: "简单", 2: "普通", 3: "困难", 4: "专家", 5: "大师"}
            diff_text = difficulties[self.ai_difficulty]
            self.status_label.config(text=f"AI回合 ({diff_text}) - {mode_text}")
        else:
            player_text = "黑棋" if self.current_player == 1 else "白棋"
            self.status_label.config(text=f"{player_text}回合 - {mode_text}")
    
    def update_info_panel(self):
        """更新信息面板"""
        self.info_text.delete(1.0, tk.END)
        
        info = f"游戏模式: {'人机对战' if self.game_mode == 'pvc' else '人人对战'}\n"
        info += f"AI难度: {self.ai_difficulty}/5\n"
        info += f"已下步数: {len(self.move_history)}\n"
        info += f"当前回合: {'黑棋' if self.current_player == 1 else '白棋'}\n\n"
        
        info += "操作提示:\n"
        info += "• 点击棋盘下棋\n"
        info += "• Ctrl+Z 悔棋\n"
        info += "• 鼠标悬停预览落子位置\n\n"
        
        if self.move_history:
            info += "最近几步:\n"
            recent_moves = self.move_history[-5:]
            for i, (x, y, player) in enumerate(recent_moves):
                player_name = "黑" if player == 1 else "白"
                step_num = len(self.move_history) - len(recent_moves) + i + 1
                info += f"{step_num}. {player_name}({chr(65+x)}{y+1})\n"
        
        self.info_text.insert(1.0, info)
    
    def run(self):
        """运行游戏"""
        self.root.mainloop()
