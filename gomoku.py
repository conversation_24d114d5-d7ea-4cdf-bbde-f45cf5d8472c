import tkinter as tk
from tkinter import messagebox
import sys
import random
import time

class GomokuGame:
    def __init__(self, mode='pvp'):
        self.root = tk.Tk()
        self.root.title("五子棋")
        self.root.resizable(False, False)
        
        # 游戏常量
        self.BOARD_SIZE = 15
        self.CELL_SIZE = 40
        self.PIECE_RADIUS = 18
        
        # 游戏状态
        self.board = [[0 for _ in range(self.BOARD_SIZE)] for _ in range(self.BOARD_SIZE)]
        self.current_player = 1  # 1: 黑棋, 2: 白棋
        self.game_over = False
        self.game_mode = mode  # 'pvp': 人人对战, 'pvc': 人机对战
        self.ai_player = 2  # AI使用白棋
        self.ai_difficulty = 3  # AI难度(1-5)
        
        # 创建AI
        self.ai = GomokuAI(self.BOARD_SIZE, self.ai_difficulty)
        
        # 创建UI
        self.setup_ui()
        
    def setup_ui(self):
        # 状态栏
        self.status_frame = tk.Frame(self.root)
        self.status_frame.pack(pady=10)
        
        self.status_label = tk.Label(self.status_frame, text="黑棋先手", font=("Arial", 14))
        self.status_label.pack()
        
        # 控制按钮
        self.control_frame = tk.Frame(self.root)
        self.control_frame.pack(pady=5)
        
        self.mode_button = tk.Button(self.control_frame, text="切换模式", command=self.toggle_mode)
        self.mode_button.pack(side=tk.LEFT, padx=5)
        
        self.difficulty_button = tk.Button(self.control_frame, text="AI难度", command=self.change_difficulty)
        self.difficulty_button.pack(side=tk.LEFT, padx=5)
        
        self.restart_button = tk.Button(self.control_frame, text="重新开始", command=self.restart_game)
        self.restart_button.pack(side=tk.LEFT, padx=5)
        
        self.quit_button = tk.Button(self.control_frame, text="退出", command=self.root.quit)
        self.quit_button.pack(side=tk.LEFT, padx=5)
        
        # 棋盘画布
        self.canvas_size = self.BOARD_SIZE * self.CELL_SIZE
        self.canvas = tk.Canvas(self.root, width=self.canvas_size, height=self.canvas_size, bg="wheat")
        self.canvas.pack(padx=10, pady=10)
        self.canvas.bind("<Button-1>", self.on_click)
        
        # 绘制棋盘
        self.draw_board()
        
    def draw_board(self):
        # 绘制网格线
        for i in range(self.BOARD_SIZE):
            # 垂直线
            x = i * self.CELL_SIZE + self.CELL_SIZE // 2
            self.canvas.create_line(x, self.CELL_SIZE // 2, x, self.canvas_size - self.CELL_SIZE // 2, fill="black")
            # 水平线
            y = i * self.CELL_SIZE + self.CELL_SIZE // 2
            self.canvas.create_line(self.CELL_SIZE // 2, y, self.canvas_size - self.CELL_SIZE // 2, y, fill="black")
        
        # 绘制星位
        star_positions = [3, 7, 11]
        for i in star_positions:
            for j in star_positions:
                x = i * self.CELL_SIZE + self.CELL_SIZE // 2
                y = j * self.CELL_SIZE + self.CELL_SIZE // 2
                self.canvas.create_oval(x-3, y-3, x+3, y+3, fill="black")
    
    def on_click(self, event):
        if self.game_over:
            return
            
        # 计算点击位置对应的棋盘坐标
        x = event.x // self.CELL_SIZE
        y = event.y // self.CELL_SIZE
        
        if 0 <= x < self.BOARD_SIZE and 0 <= y < self.BOARD_SIZE:
            if self.board[y][x] == 0:  # 空位置
                self.place_piece(x, y)
    
    def place_piece(self, x, y):
        # 在棋盘数组中记录
        self.board[y][x] = self.current_player
        
        # 在画布上绘制棋子
        canvas_x = x * self.CELL_SIZE + self.CELL_SIZE // 2
        canvas_y = y * self.CELL_SIZE + self.CELL_SIZE // 2
        
        color = "black" if self.current_player == 1 else "white"
        self.canvas.create_oval(
            canvas_x - self.PIECE_RADIUS, canvas_y - self.PIECE_RADIUS,
            canvas_x + self.PIECE_RADIUS, canvas_y + self.PIECE_RADIUS,
            fill=color, outline="black", width=2
        )
        
        # 检查胜负
        if self.check_win(x, y):
            winner = "黑棋" if self.current_player == 1 else "白棋"
            self.status_label.config(text=f"{winner}获胜!")
            self.game_over = True
            messagebox.showinfo("游戏结束", f"{winner}获胜!")
            return
        
        # 切换玩家
        self.current_player = 3 - self.current_player
        
        # 更新状态显示
        self.update_status()
        
        # 如果轮到AI且游戏未结束，让AI下棋
        if self.current_player == self.ai_player and not self.game_over and self.game_mode == 'pvc':
            self.root.after(500, self.ai_move)  # 延迟500ms让AI思考
    
    def check_win(self, x, y):
        # 检查四个方向：水平、垂直、两个对角线
        directions = [
            (1, 0),   # 水平
            (0, 1),   # 垂直
            (1, 1),   # 主对角线
            (1, -1)   # 副对角线
        ]
        
        for dx, dy in directions:
            count = 1  # 当前位置的棋子
            
            # 正方向检查
            i, j = x + dx, y + dy
            while 0 <= i < self.BOARD_SIZE and 0 <= j < self.BOARD_SIZE and self.board[j][i] == self.current_player:
                count += 1
                i += dx
                j += dy
            
            # 反方向检查
            i, j = x - dx, y - dy
            while 0 <= i < self.BOARD_SIZE and 0 <= j < self.BOARD_SIZE and self.board[j][i] == self.current_player:
                count += 1
                i -= dx
                j -= dy
            
            if count >= 5:
                return True
        
        return False
    
    def restart_game(self):
        # 重置游戏状态
        self.board = [[0 for _ in range(self.BOARD_SIZE)] for _ in range(self.BOARD_SIZE)]
        self.current_player = 1
        self.game_over = False
        
        # 清除棋盘上的棋子
        self.canvas.delete("all")
        self.draw_board()
        
        # 更新状态
        self.update_status()
        
        # 如果是人机对战且AI先手，让AI下第一步
        if self.game_mode == 'pvc' and self.ai_player == 1 and not self.game_over:
            self.root.after(500, self.ai_move)
    
    def toggle_mode(self):
        self.game_mode = 'pvc' if self.game_mode == 'pvp' else 'pvp'
        mode_text = "人机对战" if self.game_mode == 'pvc' else "人人对战"
        messagebox.showinfo("游戏模式", f"切换到{mode_text}模式")
        self.restart_game()
    
    def change_difficulty(self):
        difficulties = {1: "简单", 2: "普通", 3: "困难", 4: "专家", 5: "大师"}
        current = difficulties[self.ai_difficulty]
        
        # 循环切换难度
        self.ai_difficulty = self.ai_difficulty % 5 + 1
        self.ai.difficulty = self.ai_difficulty
        
        new_difficulty = difficulties[self.ai_difficulty]
        messagebox.showinfo("AI难度", f"难度从{current}切换到{new_difficulty}")
    
    def update_status(self):
        if self.game_over:
            return
            
        mode_text = "人机对战" if self.game_mode == 'pvc' else "人人对战"
        
        if self.current_player == self.ai_player and self.game_mode == 'pvc':
            difficulties = {1: "简单", 2: "普通", 3: "困难", 4: "专家", 5: "大师"}
            diff_text = difficulties[self.ai_difficulty]
            self.status_label.config(text=f"AI思考中... ({diff_text})")
        else:
            player_text = "黑棋" if self.current_player == 1 else "白棋"
            self.status_label.config(text=f"{player_text}回合 ({mode_text})")
    
    def ai_move(self):
        if self.game_over:
            return
            
        # 获取AI的最佳落子位置
        x, y = self.ai.get_best_move(self.board, self.ai_player)
        
        if x is not None and y is not None:
            self.place_piece(x, y)
    
    def run(self):
        self.root.mainloop()

class GomokuAI:
    def __init__(self, board_size, difficulty=3):
        self.board_size = board_size
        self.difficulty = difficulty
        self.max_depth = difficulty  # 搜索深度与难度相关
        
        # 重新设计的棋型评分系统
        self.pattern_scores = {
            # 进攻型评分
            'five': 1000000,     # 五连（必胜）
            'open_four': 100000, # 活四（必胜）
            'double_four': 80000, # 双冲四
            'four_three': 50000,  # 冲四活三
            'double_three': 30000, # 双活三
            'block_four': 5000,   # 被堵的四子
            'open_three': 1000,   # 活三
            'block_three': 500,   # 被堵的三子
            'open_two': 100,      # 活二
            'block_two': 50,      # 被堵的二子
            'one': 1,            # 单子
            
            # 防守型评分（更高权重）
            'defend_five': 2000000,  # 防守五连
            'defend_four': 150000,   # 防守活四
            'defend_three': 5000,    # 防守活三
            'defend_two': 200        # 防守活二
        }
        
        # 方向向量
        self.directions = [(1, 0), (0, 1), (1, 1), (1, -1)]
    
    def get_best_move(self, board, player):
        """获取AI的最佳落子位置"""
        opponent = 3 - player
        
        # 如果是第一步，选择棋盘中心位置
        if self.is_empty_board(board):
            center = self.board_size // 2
            return center, center
        
        # 1. 首先检查是否能直接获胜
        winning_move = self.find_winning_move(board, player)
        if winning_move:
            return winning_move
        
        # 2. 检查对手是否能直接获胜，必须防守
        defense_move = self.find_critical_defense(board, player)
        if defense_move:
            return defense_move
        
        # 3. 检查自己是否能完成4连（最高优先级进攻）
        four_move = self.find_existing_sequence_completion(board, player, 4)
        if four_move:
            return four_move
        
        # 4. 检查对手是否能完成4连，必须防守
        opponent_four_move = self.find_existing_sequence_completion(board, 3 - player, 4)
        if opponent_four_move:
            return opponent_four_move
        
        # 5. 检查自己是否能完成3连
        three_move = self.find_existing_sequence_completion(board, player, 3)
        if three_move:
            return three_move
        
        # 6. 检查对手是否能完成3连
        opponent_three_move = self.find_existing_sequence_completion(board, 3 - player, 3)
        if opponent_three_move:
            return opponent_three_move
        
        # 7. 检查双杀威胁
        double_kill_move = self.find_double_kill_defense(board, player)
        if double_kill_move:
            return double_kill_move
        
        # 8. 寻找最佳进攻位置
        attack_move = self.find_best_attack(board, player)
        if attack_move:
            return attack_move
        
        # 9. 如果没有特殊位置，使用常规评估
        return self.find_best_evaluated_move(board, player)
    
    def minimax(self, board, depth, alpha, beta, maximizing, player):
        """Minimax算法配合Alpha-Beta剪枝"""
        if depth == 0:
            return self.evaluate_board(board, player)
        
        opponent = 3 - player
        
        if maximizing:
            max_eval = float('-inf')
            candidates = self.get_candidates(board)
            
            for x, y in candidates:
                if board[y][x] == 0:
                    board[y][x] = player
                    
                    # 检查是否获胜
                    if self.check_win(board, x, y, player):
                        board[y][x] = 0
                        return 1000000
                    
                    eval_score = self.minimax(board, depth - 1, alpha, beta, False, player)
                    board[y][x] = 0
                    
                    max_eval = max(max_eval, eval_score)
                    alpha = max(alpha, eval_score)
                    
                    if beta <= alpha:
                        break
            
            return max_eval
        else:
            min_eval = float('inf')
            candidates = self.get_candidates(board)
            
            for x, y in candidates:
                if board[y][x] == 0:
                    board[y][x] = opponent
                    
                    # 检查是否获胜
                    if self.check_win(board, x, y, opponent):
                        board[y][x] = 0
                        return -1000000
                    
                    eval_score = self.minimax(board, depth - 1, alpha, beta, True, player)
                    board[y][x] = 0
                    
                    min_eval = min(min_eval, eval_score)
                    beta = min(beta, eval_score)
                    
                    if beta <= alpha:
                        break
            
            return min_eval
    
    def evaluate_move(self, board, x, y, player):
        """评估某个位置的得分（增强版）"""
        opponent = 3 - player
        score = 0
        
        # 模拟落子
        board[y][x] = player
        
        # 检查是否获胜
        if self.check_win(board, x, y, player):
            board[y][x] = 0
            return 10000000
        
        # 检查是否能阻止对手获胜
        for dx, dy in self.directions:
            nx, ny = x + dx, y + dy
            if 0 <= nx < self.board_size and 0 <= ny < self.board_size and board[ny][nx] == 0:
                board[ny][nx] = opponent
                if self.check_win(board, nx, ny, opponent):
                    board[ny][nx] = 0
                    board[y][x] = 0
                    return 5000000  # 阻止对手获胜
                board[ny][nx] = 0
        
        # 评估进攻价值
        attack_score = self.evaluate_position_advanced(board, x, y, player)
        
        # 评估防守价值
        defense_score = self.evaluate_defense_value(board, x, y, opponent)
        
        # 综合评分（防守权重更高）
        score = attack_score + defense_score * 1.5
        
        # 位置价值评估（中心位置更有价值）
        position_score = self.evaluate_position_value(x, y)
        score += position_score
        
        board[y][x] = 0
        return score
    
    def evaluate_position_advanced(self, board, x, y, player):
        """高级位置评估"""
        score = 0
        patterns = []
        
        # 分析四个方向的棋型
        for dx, dy in self.directions:
            pattern = self.analyze_line_pattern(board, x, y, dx, dy, player)
            patterns.append(pattern)
        
        # 检查特殊组合
        if 'open_four' in patterns:
            score += self.pattern_scores['open_four']
        elif 'four' in patterns:
            score += self.pattern_scores['four']
        elif patterns.count('open_three') >= 2:
            score += self.pattern_scores['double_three']  # 双活三
        elif patterns.count('open_three') >= 1 and 'four' in patterns:
            score += self.pattern_scores['four_three']  # 冲四活三
        elif 'open_three' in patterns:
            score += self.pattern_scores['open_three']
        elif patterns.count('open_two') >= 2:
            score += self.pattern_scores['open_two'] * 2  # 双活二
        elif 'open_two' in patterns:
            score += self.pattern_scores['open_two']
        
        return score
    
    def evaluate_defense_value(self, board, x, y, opponent):
        """评估防守价值"""
        score = 0
        
        # 模拟对手在这个位置落子
        board[y][x] = opponent
        
        # 分析对手的威胁
        for dx, dy in self.directions:
            pattern = self.analyze_line_pattern(board, x, y, dx, dy, opponent)
            
            if pattern == 'open_four':
                score += self.pattern_scores['defend_four']
            elif pattern == 'open_three':
                score += self.pattern_scores['defend_three']
            elif pattern == 'open_two':
                score += self.pattern_scores['defend_two']
        
        board[y][x] = 0
        return score
    
    def evaluate_position_value(self, x, y):
        """评估位置价值（中心位置更有价值）"""
        center = self.board_size // 2
        distance_to_center = abs(x - center) + abs(y - center)
        max_distance = center * 2
        
        # 距离中心越近，价值越高
        position_score = (max_distance - distance_to_center) * 10
        return position_score
    
    def analyze_line_pattern(self, board, x, y, dx, dy, player):
        """分析某个方向的棋型"""
        line = self.get_line_segment(board, x, y, dx, dy, player)
        if not line:
            return 'none'
        
        return self.classify_pattern(line, player)
    
    def get_line_segment(self, board, x, y, dx, dy, player):
        """获取线段"""
        segment = []
        
        # 向两个方向延伸
        for direction in [-1, 1]:
            nx, ny = x, y
            for i in range(6):
                if direction == -1 and i == 0:
                    continue  # 跳过起始位置
                
                nx += dx * direction
                ny += dy * direction
                
                if 0 <= nx < self.board_size and 0 <= ny < self.board_size:
                    segment.append(board[ny][nx])
                else:
                    segment.append(-1)  # 边界
        
        return segment
    
    def classify_pattern(self, line, player):
        """分类棋型"""
        opponent = 3 - player
        
        # 统计连续棋子
        consecutive = 0
        spaces = 0
        blocked = 0
        
        for cell in line:
            if cell == player:
                consecutive += 1
            elif cell == 0:
                spaces += 1
            else:
                blocked += 1
                if consecutive > 0:
                    break
        
        # 根据连续数和空位数判断棋型
        if consecutive >= 5:
            return 'five'
        elif consecutive == 4:
            if spaces >= 1 and blocked == 0:
                return 'open_four'
            else:
                return 'four'
        elif consecutive == 3:
            if spaces >= 2 and blocked == 0:
                return 'open_three'
            else:
                return 'three'
        elif consecutive == 2:
            if spaces >= 2 and blocked == 0:
                return 'open_two'
            else:
                return 'two'
        
        return 'none'
    
    def find_four_completion_move(self, board, player):
        """寻找能完成4连的位置"""
        for y in range(self.board_size):
            for x in range(self.board_size):
                if board[y][x] == 0:
                    board[y][x] = player
                    
                    # 检查是否能形成4连
                    if self.has_four_in_row(board, x, y, player):
                        board[y][x] = 0
                        return x, y
                    
                    board[y][x] = 0
        
        return None
    
    def find_opponent_four_defense(self, board, player):
        """寻找防守对手4连的位置"""
        opponent = 3 - player
        
        for y in range(self.board_size):
            for x in range(self.board_size):
                if board[y][x] == 0:
                    board[y][x] = opponent
                    
                    # 检查对手是否能形成4连
                    if self.has_four_in_row(board, x, y, opponent):
                        board[y][x] = 0
                        return x, y
                    
                    board[y][x] = 0
        
        return None
    
    def find_three_completion_move(self, board, player):
        """寻找能完成3连的位置"""
        for y in range(self.board_size):
            for x in range(self.board_size):
                if board[y][x] == 0:
                    board[y][x] = player
                    
                    # 检查是否能形成3连
                    if self.has_three_in_row(board, x, y, player):
                        board[y][x] = 0
                        return x, y
                    
                    board[y][x] = 0
        
        return None
    
    def find_opponent_three_defense(self, board, player):
        """寻找防守对手3连的位置"""
        opponent = 3 - player
        
        for y in range(self.board_size):
            for x in range(self.board_size):
                if board[y][x] == 0:
                    board[y][x] = opponent
                    
                    # 检查对手是否能形成3连
                    if self.has_three_in_row(board, x, y, opponent):
                        board[y][x] = 0
                        return x, y
                    
                    board[y][x] = 0
        
        return None
    
    def find_critical_defense(self, board, player):
        """寻找关键防守位置（对手下一步能获胜）"""
        opponent = 3 - player
        
        for y in range(self.board_size):
            for x in range(self.board_size):
                if board[y][x] == 0:
                    # 模拟对手落子
                    board[y][x] = opponent
                    
                    # 如果对手能获胜，必须防守
                    if self.check_win(board, x, y, opponent):
                        board[y][x] = 0
                        return x, y
                    
                    board[y][x] = 0
        
        return None
    
    def find_threat_defense(self, board, player):
        """寻找威胁防守位置（对手的连子威胁）"""
        opponent = 3 - player
        
        # 首先寻找对手的4连威胁
        for y in range(self.board_size):
            for x in range(self.board_size):
                if board[y][x] == 0:
                    board[y][x] = opponent
                    
                    # 检查是否能形成4连
                    if self.has_four_in_row(board, x, y, opponent):
                        board[y][x] = 0
                        return x, y
                    
                    board[y][x] = 0
        
        # 寻找对手的3连威胁
        for y in range(self.board_size):
            for x in range(self.board_size):
                if board[y][x] == 0:
                    board[y][x] = opponent
                    
                    # 检查是否能形成3连
                    if self.has_three_in_row(board, x, y, opponent):
                        board[y][x] = 0
                        return x, y
                    
                    board[y][x] = 0
        
        # 寻找对手的2连威胁
        for y in range(self.board_size):
            for x in range(self.board_size):
                if board[y][x] == 0:
                    board[y][x] = opponent
                    
                    # 检查是否能形成2连
                    if self.has_two_in_row(board, x, y, opponent):
                        board[y][x] = 0
                        return x, y
                    
                    board[y][x] = 0
        
        return None
    
    def has_four_in_row(self, board, x, y, player):
        """检查是否有4连"""
        for dx, dy in self.directions:
            count = 1
            
            # 正方向
            nx, ny = x + dx, y + dy
            while 0 <= nx < self.board_size and 0 <= ny < self.board_size:
                if board[ny][nx] == player:
                    count += 1
                elif board[ny][nx] == 0:
                    break
                else:
                    break
                nx += dx
                ny += dy
            
            # 反方向
            nx, ny = x - dx, y - dy
            while 0 <= nx < self.board_size and 0 <= ny < self.board_size:
                if board[ny][nx] == player:
                    count += 1
                elif board[ny][nx] == 0:
                    break
                else:
                    break
                nx -= dx
                ny -= dy
            
            if count >= 4:
                return True
        
        return False
    
    def find_existing_sequence_completion(self, board, player, target_length):
        """寻找能完成已有连子的位置"""
        for y in range(self.board_size):
            for x in range(self.board_size):
                if board[y][x] == 0:
                    # 检查这个位置是否能完成连子
                    if self.can_complete_sequence(board, x, y, player, target_length):
                        return x, y
        
        return None
    
    def can_complete_sequence(self, board, x, y, player, target_length):
        """检查是否能完成指定长度的连子"""
        for dx, dy in self.directions:
            # 计算在这个方向上的连子数
            count = 1  # 包括当前位置
            
            # 正方向
            nx, ny = x + dx, y + dy
            while 0 <= nx < self.board_size and 0 <= ny < self.board_size:
                if board[ny][nx] == player:
                    count += 1
                elif board[ny][nx] == 0:
                    break
                else:
                    break
                nx += dx
                ny += dy
            
            # 反方向
            nx, ny = x - dx, y - dy
            while 0 <= nx < self.board_size and 0 <= ny < self.board_size:
                if board[ny][nx] == player:
                    count += 1
                elif board[ny][nx] == 0:
                    break
                else:
                    break
                nx -= dx
                ny -= dy
            
            if count >= target_length:
                return True
        
        return False
    
    def has_three_in_row(self, board, x, y, player):
        """检查是否有3连"""
        for dx, dy in self.directions:
            count = 1
            
            # 正方向
            nx, ny = x + dx, y + dy
            while 0 <= nx < self.board_size and 0 <= ny < self.board_size:
                if board[ny][nx] == player:
                    count += 1
                elif board[ny][nx] == 0:
                    break
                else:
                    break
                nx += dx
                ny += dy
            
            # 反方向
            nx, ny = x - dx, y - dy
            while 0 <= nx < self.board_size and 0 <= ny < self.board_size:
                if board[ny][nx] == player:
                    count += 1
                elif board[ny][nx] == 0:
                    break
                else:
                    break
                nx -= dx
                ny -= dy
            
            if count >= 3:
                return True
        
        return False
    
    def has_two_in_row(self, board, x, y, player):
        """检查是否有2连"""
        for dx, dy in self.directions:
            count = 1
            
            # 正方向
            nx, ny = x + dx, y + dy
            while 0 <= nx < self.board_size and 0 <= ny < self.board_size:
                if board[ny][nx] == player:
                    count += 1
                elif board[ny][nx] == 0:
                    break
                else:
                    break
                nx += dx
                ny += dy
            
            # 反方向
            nx, ny = x - dx, y - dy
            while 0 <= nx < self.board_size and 0 <= ny < self.board_size:
                if board[ny][nx] == player:
                    count += 1
                elif board[ny][nx] == 0:
                    break
                else:
                    break
                nx -= dx
                ny -= dy
            
            if count >= 2:
                return True
        
        return False
    
    def find_best_attack(self, board, player):
        """寻找最佳进攻位置"""
        best_move = None
        best_score = -1
        
        for y in range(self.board_size):
            for x in range(self.board_size):
                if board[y][x] == 0:
                    board[y][x] = player
                    
                    # 评估这个位置的进攻价值
                    score = self.evaluate_attack_potential(board, x, y, player)
                    
                    board[y][x] = 0
                    
                    if score > best_score:
                        best_score = score
                        best_move = (x, y)
        
        return best_move if best_score > 100 else None
    
    def evaluate_attack_potential(self, board, x, y, player):
        """评估进攻潜力"""
        score = 0
        
        # 检查是否能形成4连
        if self.has_four_in_row(board, x, y, player):
            score += 10000
        
        # 检查是否能形成3连
        if self.has_three_in_row(board, x, y, player):
            score += 1000
        
        # 检查是否能形成2连
        if self.has_two_in_row(board, x, y, player):
            score += 100
        
        return score
    
    def find_best_evaluated_move(self, board, player):
        """寻找最佳评估位置"""
        candidates = self.get_candidates(board)
        
        if not candidates:
            return None, None
        
        best_score = float('-inf')
        best_move = None
        
        for x, y in candidates:
            # 模拟落子
            board[y][x] = player
            
            # 使用增强的评估函数
            score = self.evaluate_move(board, x, y, player)
            
            # 撤销落子
            board[y][x] = 0
            
            if score > best_score:
                best_score = score
                best_move = (x, y)
        
        return best_move
    
    def find_winning_move(self, board, player):
        """寻找必胜位置"""
        for y in range(self.board_size):
            for x in range(self.board_size):
                if board[y][x] == 0:
                    board[y][x] = player
                    if self.check_win(board, x, y, player):
                        board[y][x] = 0
                        return x, y
                    board[y][x] = 0
        
        return None
    
    def has_open_four(self, board, x, y, player):
        """检查是否有活四"""
        for dx, dy in self.directions:
            line = self.get_line_segment(board, x, y, dx, dy, player)
            if self.classify_pattern(line, player) == 'open_four':
                return True
        return False
    
    def find_double_kill_defense(self, board, player):
        """寻找双杀防守位置"""
        opponent = 3 - player
        
        # 检查对手是否有双杀威胁
        for y in range(self.board_size):
            for x in range(self.board_size):
                if board[y][x] == 0:
                    # 模拟对手落子
                    board[y][x] = opponent
                    
                    # 检查是否形成双杀
                    if self.has_double_kill(board, x, y, opponent):
                        board[y][x] = 0
                        return x, y
                    
                    board[y][x] = 0
        
        return None
    
    def has_double_kill(self, board, x, y, player):
        """检查是否有双杀威胁"""
        threat_count = 0
        
        # 统计威胁数量
        for dy in range(self.board_size):
            for dx in range(self.board_size):
                if board[dy][dx] == 0:
                    board[dy][dx] = player
                    
                    # 检查是否能形成五连或活四
                    if self.check_win(board, dx, dy, player) or self.has_open_four(board, dx, dy, player):
                        threat_count += 1
                    
                    board[dy][dx] = 0
                    
                    if threat_count >= 2:
                        return True
        
        return False
    
    def find_fork_attack(self, board, player):
        """寻找分叉攻击位置"""
        best_fork = None
        max_threats = 0
        
        for y in range(self.board_size):
            for x in range(self.board_size):
                if board[y][x] == 0:
                    board[y][x] = player
                    
                    # 统计从这个位置能形成的威胁数量
                    threats = self.count_threats_from_position(board, x, y, player)
                    
                    board[y][x] = 0
                    
                    if threats > max_threats:
                        max_threats = threats
                        best_fork = (x, y)
        
        return best_fork if max_threats >= 2 else None
    
    def count_threats_from_position(self, board, x, y, player):
        """统计从某个位置能形成的威胁数量"""
        threat_count = 0
        
        # 检查四个方向
        for dx, dy in self.directions:
            # 检查是否能形成活四
            if self.can_form_open_four(board, x, y, dx, dy, player):
                threat_count += 1
            
            # 检查是否能形成活三
            if self.can_form_open_three(board, x, y, dx, dy, player):
                threat_count += 1
        
        return threat_count
    
    def can_form_open_four(self, board, x, y, dx, dy, player):
        """检查是否能形成活四"""
        # 模拟在这个方向延伸
        consecutive = 1
        spaces = 0
        
        # 正方向
        nx, ny = x + dx, y + dy
        while 0 <= nx < self.board_size and 0 <= ny < self.board_size:
            if board[ny][nx] == player:
                consecutive += 1
            elif board[ny][nx] == 0:
                spaces += 1
            else:
                break
            nx += dx
            ny += dy
        
        # 反方向
        nx, ny = x - dx, y - dy
        while 0 <= nx < self.board_size and 0 <= ny < self.board_size:
            if board[ny][nx] == player:
                consecutive += 1
            elif board[ny][nx] == 0:
                spaces += 1
            else:
                break
            nx -= dx
            ny -= dy
        
        # 判断是否能形成活四
        return consecutive >= 3 and spaces >= 1
    
    def can_form_open_three(self, board, x, y, dx, dy, player):
        """检查是否能形成活三"""
        consecutive = 1
        spaces = 0
        
        # 正方向
        nx, ny = x + dx, y + dy
        while 0 <= nx < self.board_size and 0 <= ny < self.board_size:
            if board[ny][nx] == player:
                consecutive += 1
            elif board[ny][nx] == 0:
                spaces += 1
            else:
                break
            nx += dx
            ny += dy
        
        # 反方向
        nx, ny = x - dx, y - dy
        while 0 <= nx < self.board_size and 0 <= ny < self.board_size:
            if board[ny][nx] == player:
                consecutive += 1
            elif board[ny][nx] == 0:
                spaces += 1
            else:
                break
            nx -= dx
            ny -= dy
        
        # 判断是否能形成活三
        return consecutive >= 2 and spaces >= 2
    
    def evaluate_player(self, board, player):
        """评估某个玩家的得分"""
        score = 0
        
        # 遍历所有位置
        for y in range(self.board_size):
            for x in range(self.board_size):
                if board[y][x] == player:
                    score += self.evaluate_position(board, x, y, player)
        
        return score
    
    def evaluate_position(self, board, x, y, player):
        """评估某个位置的得分"""
        score = 0
        
        # 检查四个方向
        for dx, dy in self.directions:
            line_score = self.evaluate_line(board, x, y, dx, dy, player)
            score += line_score
        
        return score
    
    def evaluate_line(self, board, x, y, dx, dy, player):
        """评估某个方向的得分"""
        # 获取该方向的线段
        line = self.get_line(board, x, y, dx, dy, player)
        if not line:
            return 0
        
        # 分析棋型
        pattern = self.analyze_pattern(line, player)
        return self.pattern_scores.get(pattern, 0)
    
    def get_line(self, board, x, y, dx, dy, player):
        """获取某个方向的线段"""
        line = []
        
        # 向两个方向延伸
        for direction in [-1, 1]:
            nx, ny = x, y
            for _ in range(6):  # 最多看6步
                if direction == -1 and (nx != x or ny != y):
                    break  # 避免重复添加起始位置
                
                nx += dx * direction
                ny += dy * direction
                
                if 0 <= nx < self.board_size and 0 <= ny < self.board_size:
                    line.append(board[ny][nx])
                else:
                    line.append(-1)  # 边界
        
        return line
    
    def analyze_pattern(self, line, player):
        """分析棋型"""
        opponent = 3 - player
        
        # 连续的棋子数
        count = 0
        open_ends = 0
        
        for i, cell in enumerate(line):
            if cell == player:
                count += 1
            elif cell == 0:
                open_ends += 1
            else:
                break
        
        # 根据连续棋子数和开放端判断棋型
        if count >= 5:
            return 'five'
        elif count == 4:
            if open_ends >= 1:
                return 'open_four'
            else:
                return 'four'
        elif count == 3:
            if open_ends >= 2:
                return 'open_three'
            else:
                return 'three'
        elif count == 2:
            if open_ends >= 2:
                return 'open_two'
            else:
                return 'two'
        elif count == 1:
            return 'one'
        
        return 0
    
    def get_candidates(self, board):
        """获取候选落子位置（优化版）"""
        candidates = []
        urgent_candidates = []
        normal_candidates = []
        
        # 遍历棋盘，找到有棋子附近的空位
        for y in range(self.board_size):
            for x in range(self.board_size):
                if board[y][x] == 0 and self.has_neighbor(board, x, y):
                    # 检查是否为紧急位置
                    if self.is_urgent_position(board, x, y):
                        urgent_candidates.append((x, y))
                    else:
                        normal_candidates.append((x, y))
        
        # 优先考虑紧急位置
        candidates = urgent_candidates + normal_candidates
        
        # 根据难度限制候选数量
        max_candidates = min(15 + self.difficulty * 5, len(candidates))
        if len(candidates) > max_candidates:
            candidates = candidates[:max_candidates]
        
        return candidates
    
    def is_urgent_position(self, board, x, y):
        """检查是否为紧急位置"""
        # 检查周围是否有形成威胁的棋子
        for dy in range(-2, 3):
            for dx in range(-2, 3):
                if dx == 0 and dy == 0:
                    continue
                
                nx, ny = x + dx, y + dy
                if 0 <= nx < self.board_size and 0 <= ny < self.board_size:
                    if board[ny][nx] != 0:
                        # 检查这个位置是否能形成威胁
                        return True
        
        return False
    
    def has_neighbor(self, board, x, y):
        """检查位置是否有邻居"""
        for dy in range(-2, 3):
            for dx in range(-2, 3):
                if dx == 0 and dy == 0:
                    continue
                
                nx, ny = x + dx, y + dy
                if 0 <= nx < self.board_size and 0 <= ny < self.board_size:
                    if board[ny][nx] != 0:
                        return True
        
        return False
    
    def check_win(self, board, x, y, player):
        """检查是否获胜"""
        for dx, dy in self.directions:
            count = 1
            
            # 正方向
            nx, ny = x + dx, y + dy
            while 0 <= nx < self.board_size and 0 <= ny < self.board_size and board[ny][nx] == player:
                count += 1
                nx += dx
                ny += dy
            
            # 反方向
            nx, ny = x - dx, y - dy
            while 0 <= nx < self.board_size and 0 <= ny < self.board_size and board[ny][nx] == player:
                count += 1
                nx -= dx
                ny -= dy
            
            if count >= 5:
                return True
        
        return False
    
    def is_empty_board(self, board):
        """检查棋盘是否为空"""
        for row in board:
            if any(cell != 0 for cell in row):
                return False
        return True

if __name__ == "__main__":
    game = GomokuGame('pvc')  # 默认人机对战模式
    game.run()