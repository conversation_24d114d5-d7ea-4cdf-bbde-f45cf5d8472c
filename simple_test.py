"""
简单的五子棋游戏测试
验证基本功能是否正常
"""

import tkinter as tk
from gomoku_game import GomokuGame
import threading
import time

def test_gui_creation():
    """测试GUI创建"""
    print("测试GUI创建...")
    
    try:
        # 创建游戏实例
        game = GomokuGame()
        
        # 设置窗口在3秒后自动关闭
        def auto_close():
            time.sleep(3)
            game.root.quit()
        
        # 在后台线程中运行自动关闭
        threading.Thread(target=auto_close, daemon=True).start()
        
        print("GUI创建成功，3秒后自动关闭...")
        game.run()
        
        print("✓ GUI测试通过")
        return True
        
    except Exception as e:
        print(f"✗ GUI测试失败: {e}")
        return False

def test_ai_basic():
    """测试AI基本功能"""
    print("测试AI基本功能...")
    
    try:
        game = GomokuGame()
        
        # 测试AI获取最佳位置
        x, y = game.ai.get_best_move(game.board, 2)
        
        if x is not None and y is not None:
            print(f"✓ AI能正常返回位置: ({x}, {y})")
            return True
        else:
            print("✗ AI返回了无效位置")
            return False
            
    except Exception as e:
        print(f"✗ AI测试失败: {e}")
        return False

def test_win_detection():
    """测试胜负检测"""
    print("测试胜负检测...")
    
    try:
        game = GomokuGame()
        
        # 创建五连
        for i in range(5):
            game.board[7][5+i] = 1
        
        # 测试胜负检测
        result = game.check_win(7, 7, 1)
        
        if result:
            print("✓ 胜负检测正常")
            return True
        else:
            print("✗ 胜负检测失败")
            return False
            
    except Exception as e:
        print(f"✗ 胜负检测测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("五子棋游戏简单测试")
    print("=" * 30)
    
    tests = [
        ("AI基本功能", test_ai_basic),
        ("胜负检测", test_win_detection),
        ("GUI创建", test_gui_creation),  # 放在最后，因为会显示窗口
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        if test_func():
            passed += 1
    
    print("\n" + "=" * 30)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！游戏功能正常！")
    else:
        print("⚠️  部分测试失败，请检查问题")

if __name__ == "__main__":
    main()
