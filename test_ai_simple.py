"""
简化的AI测试，不使用超时机制
"""

from gomoku_ai import GomokuAI
import copy

def print_board(board, title=""):
    """打印棋盘"""
    if title:
        print(f"{title}:")
    print("   ", end="")
    for i in range(15):
        print(f"{i:2}", end="")
    print()
    
    for i, row in enumerate(board):
        print(f"{i:2} ", end="")
        for cell in row:
            if cell == 0:
                print(" .", end="")
            elif cell == 1:
                print(" ●", end="")
            else:
                print(" ○", end="")
        print()
    print()

def test_ai_without_timeout():
    """测试AI不使用超时机制"""
    print("=== 测试AI不使用超时机制 ===")
    
    # 创建AI，设置较低的难度以减少搜索时间
    ai = GomokuAI(15, 1)  # 难度1，搜索深度较浅
    ai.time_limit = 100  # 设置很长的超时时间，基本不会超时
    
    # 创建简单棋盘
    board = [[0 for _ in range(15)] for _ in range(15)]
    board[7][7] = 1  # 黑棋
    board[7][8] = 2  # 白棋
    
    # 保存原始状态
    original_board = copy.deepcopy(board)
    
    print_board(board, "原始棋盘")
    
    # 测试AI获取最佳位置
    print("测试AI获取最佳位置...")
    x, y = ai.get_best_move(board, 2)
    
    print(f"AI选择位置: ({x}, {y})")
    print_board(board, "AI计算后的棋盘")
    
    # 检查是否相同
    if board == original_board:
        print("✓ AI计算没有修改棋盘")
        
        # 检查AI选择的位置是否合理
        if x is not None and y is not None and 0 <= x < 15 and 0 <= y < 15:
            if board[y][x] == 0:
                print("✓ AI选择了有效的空位置")
                return True
            else:
                print(f"✗ AI选择了已有棋子的位置，该位置值为: {board[y][x]}")
                return False
        else:
            print("✗ AI返回了无效位置")
            return False
    else:
        print("✗ AI计算修改了棋盘!")
        return False

def test_individual_ai_functions():
    """测试AI的各个函数"""
    print("=== 测试AI的各个函数 ===")
    
    ai = GomokuAI(15, 1)
    
    # 创建简单棋盘
    board = [[0 for _ in range(15)] for _ in range(15)]
    board[7][7] = 1  # 黑棋
    board[7][8] = 2  # 白棋
    
    original_board = copy.deepcopy(board)
    
    print_board(board, "原始棋盘")
    
    # 测试find_winning_move
    print("测试find_winning_move...")
    winning_move = ai.find_winning_move(board, 2)
    print(f"必胜手: {winning_move}")
    
    if board != original_board:
        print("✗ find_winning_move修改了棋盘!")
        print_board(board, "修改后的棋盘")
        return False
    
    # 测试find_critical_defense
    print("测试find_critical_defense...")
    defense_move = ai.find_critical_defense(board, 2)
    print(f"必防手: {defense_move}")
    
    if board != original_board:
        print("✗ find_critical_defense修改了棋盘!")
        print_board(board, "修改后的棋盘")
        return False
    
    # 测试vcf_search
    print("测试vcf_search...")
    vcf_move = ai.vcf_search(board, 2)
    print(f"VCF手: {vcf_move}")
    
    if board != original_board:
        print("✗ vcf_search修改了棋盘!")
        print_board(board, "修改后的棋盘")
        return False
    
    print("✓ 所有AI函数都没有修改棋盘")
    return True

def test_minimax_simple():
    """测试简单的minimax搜索"""
    print("=== 测试简单的minimax搜索 ===")
    
    ai = GomokuAI(15, 1)
    ai.max_depth = 1  # 只搜索1层
    ai.time_limit = 100  # 很长的超时时间
    
    # 创建简单棋盘
    board = [[0 for _ in range(15)] for _ in range(15)]
    board[7][7] = 1  # 黑棋
    board[7][8] = 2  # 白棋
    
    original_board = copy.deepcopy(board)
    
    print_board(board, "原始棋盘")
    
    # 测试minimax_root
    print("测试minimax_root...")
    import time
    start_time = time.time()
    
    try:
        move, score = ai.minimax_root(board, 2, 1, start_time)
        print(f"Minimax结果: 位置{move}, 分数{score}")
        
        if board == original_board:
            print("✓ minimax_root没有修改棋盘")
            return True
        else:
            print("✗ minimax_root修改了棋盘!")
            print_board(board, "修改后的棋盘")
            return False
            
    except Exception as e:
        print(f"✗ minimax_root出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("简化的AI测试")
    print("=" * 50)
    
    tests = [
        ("AI各个函数", test_individual_ai_functions),
        ("简单minimax搜索", test_minimax_simple),
        ("AI不使用超时", test_ai_without_timeout),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 出错: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！AI功能正常！")
    else:
        print("⚠️  部分测试失败，AI存在问题")

if __name__ == "__main__":
    main()
