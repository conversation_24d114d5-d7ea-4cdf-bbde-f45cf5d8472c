"""
测试胜负判断逻辑
"""

from gomoku_game import <PERSON>mokuGame

def print_board(board):
    """打印棋盘"""
    print("   ", end="")
    for i in range(15):
        print(f"{i:2}", end="")
    print()
    
    for i, row in enumerate(board):
        print(f"{i:2} ", end="")
        for cell in row:
            if cell == 0:
                print(" .", end="")
            elif cell == 1:
                print(" ●", end="")
            else:
                print(" ○", end="")
        print()
    print()

def test_horizontal_win():
    """测试水平五连"""
    print("=== 测试水平五连 ===")
    
    game = GomokuGame()
    
    # 创建水平四连
    game.board[7][5] = 1
    game.board[7][6] = 1
    game.board[7][7] = 1
    game.board[7][8] = 1
    
    print("当前棋盘（黑棋四连）:")
    print_board(game.board)
    
    # 测试在左边补齐是否获胜
    result = game.check_win(4, 7, 1)
    print(f"在(4,7)放黑棋是否获胜: {result}")
    
    # 测试在右边补齐是否获胜
    result = game.check_win(9, 7, 1)
    print(f"在(9,7)放黑棋是否获胜: {result}")
    
    # 测试白棋在同样位置是否获胜
    result = game.check_win(4, 7, 2)
    print(f"在(4,7)放白棋是否获胜: {result}")
    
    print()

def test_vertical_win():
    """测试垂直五连"""
    print("=== 测试垂直五连 ===")
    
    game = GomokuGame()
    
    # 创建垂直四连
    game.board[5][7] = 2
    game.board[6][7] = 2
    game.board[7][7] = 2
    game.board[8][7] = 2
    
    print("当前棋盘（白棋垂直四连）:")
    print_board(game.board)
    
    # 测试在上边补齐是否获胜
    result = game.check_win(7, 4, 2)
    print(f"在(7,4)放白棋是否获胜: {result}")
    
    # 测试在下边补齐是否获胜
    result = game.check_win(7, 9, 2)
    print(f"在(7,9)放白棋是否获胜: {result}")
    
    print()

def test_diagonal_win():
    """测试对角线五连"""
    print("=== 测试对角线五连 ===")
    
    game = GomokuGame()
    
    # 创建主对角线四连
    game.board[5][5] = 1
    game.board[6][6] = 1
    game.board[7][7] = 1
    game.board[8][8] = 1
    
    print("当前棋盘（黑棋主对角线四连）:")
    print_board(game.board)
    
    # 测试补齐是否获胜
    result = game.check_win(4, 4, 1)
    print(f"在(4,4)放黑棋是否获胜: {result}")
    
    result = game.check_win(9, 9, 1)
    print(f"在(9,9)放黑棋是否获胜: {result}")
    
    print()

def test_no_win():
    """测试非获胜情况"""
    print("=== 测试非获胜情况 ===")
    
    game = GomokuGame()
    
    # 创建三连
    game.board[7][5] = 1
    game.board[7][6] = 1
    game.board[7][7] = 1
    
    print("当前棋盘（黑棋三连）:")
    print_board(game.board)
    
    # 测试延续是否获胜（应该不获胜）
    result = game.check_win(8, 7, 1)
    print(f"在(8,7)放黑棋是否获胜: {result}")
    
    result = game.check_win(4, 7, 1)
    print(f"在(4,7)放黑棋是否获胜: {result}")
    
    print()

def test_blocked_four():
    """测试被阻挡的四连"""
    print("=== 测试被阻挡的四连 ===")
    
    game = GomokuGame()
    
    # 创建被阻挡的四连
    game.board[7][4] = 2  # 白棋阻挡
    game.board[7][5] = 1
    game.board[7][6] = 1
    game.board[7][7] = 1
    game.board[7][8] = 1
    
    print("当前棋盘（黑棋四连被白棋阻挡）:")
    print_board(game.board)
    
    # 测试在右边延续是否获胜（应该不获胜，因为被阻挡）
    result = game.check_win(9, 7, 1)
    print(f"在(9,7)放黑棋是否获胜: {result}")
    
    # 但是如果有五连还是应该获胜
    game.board[7][9] = 1  # 添加第五个
    result = game.check_win(9, 7, 1)
    print(f"形成五连后是否获胜: {result}")
    
    print()

def main():
    """主测试函数"""
    print("五子棋胜负判断测试")
    print("=" * 50)
    
    try:
        test_horizontal_win()
        test_vertical_win()
        test_diagonal_win()
        test_no_win()
        test_blocked_four()
        
        print("=" * 50)
        print("所有测试完成!")
        
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
