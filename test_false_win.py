"""
测试错误获胜判断的问题
"""

from gomoku_game import GomokuGame
import time

def print_board(board):
    """打印棋盘"""
    print("   ", end="")
    for i in range(15):
        print(f"{i:2}", end="")
    print()
    
    for i, row in enumerate(board):
        print(f"{i:2} ", end="")
        for cell in row:
            if cell == 0:
                print(" .", end="")
            elif cell == 1:
                print(" ●", end="")
            else:
                print(" ○", end="")
        print()
    print()

def test_early_false_win():
    """测试是否会出现过早的错误获胜判断"""
    print("=== 测试过早的错误获胜判断 ===")
    
    game = GomokuGame()
    
    # 模拟游戏开始的几步
    moves = [
        (7, 7, 1),   # 黑棋中心
        (7, 8, 2),   # 白棋应对
        (6, 7, 1),   # 黑棋
    ]
    
    print("模拟游戏前几步...")
    
    for step, (x, y, player) in enumerate(moves, 1):
        print(f"\n第{step}步: {'黑棋' if player == 1 else '白棋'}在({x},{y})落子")
        
        # 设置当前玩家
        game.current_player = player
        
        # 检查位置是否为空
        if game.board[y][x] != 0:
            print(f"错误：位置({x},{y})已有棋子!")
            continue
        
        # 放置棋子
        game.board[y][x] = player
        game.move_history.append((x, y, player))
        game.last_move = (x, y)
        
        print_board(game.board)
        
        # 检查胜负 - 这里应该不会获胜
        if game.check_win(x, y, player):
            winner = "黑棋" if player == 1 else "白棋"
            print(f"*** 错误！{winner}在第{step}步就获胜了！这是不可能的！***")
            return False
        else:
            print(f"✓ 第{step}步后没有获胜，正确")
    
    print("\n✓ 前几步没有出现错误获胜判断")
    return True

def test_ai_evaluation():
    """测试AI评估是否正常"""
    print("=== 测试AI评估功能 ===")
    
    game = GomokuGame()
    
    # 设置一个简单的局面
    game.board[7][7] = 1  # 黑棋
    game.board[7][8] = 2  # 白棋
    
    print("当前棋盘:")
    print_board(game.board)
    
    # 测试AI评估
    print("测试AI获取最佳位置...")
    start_time = time.time()
    
    try:
        x, y = game.ai.get_best_move(game.board, 2)
        end_time = time.time()
        
        print(f"AI选择位置: ({x}, {y})")
        print(f"思考时间: {end_time - start_time:.3f}秒")
        
        # 检查AI选择的位置是否合理
        if x is not None and y is not None and 0 <= x < 15 and 0 <= y < 15:
            print(f"检查位置({x},{y})的状态: {game.board[y][x]}")
            if game.board[y][x] == 0:
                print("✓ AI选择了有效的空位置")
                return True
            else:
                print(f"✗ AI选择了已有棋子的位置，该位置值为: {game.board[y][x]}")
                print("当前棋盘状态:")
                print_board(game.board)
                return False
        else:
            print("✗ AI返回了无效位置")
            return False
            
    except Exception as e:
        print(f"✗ AI评估出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_manual_game_simulation():
    """手动模拟游戏过程"""
    print("=== 手动模拟游戏过程 ===")
    
    game = GomokuGame()
    
    # 模拟一个更长的游戏序列
    moves = [
        (7, 7, 1),   # 黑棋中心
        (8, 8, 2),   # 白棋对角
        (6, 6, 1),   # 黑棋
        (9, 9, 2),   # 白棋
        (8, 7, 1),   # 黑棋
        (7, 8, 2),   # 白棋
        (9, 8, 1),   # 黑棋
        (6, 7, 2),   # 白棋
    ]
    
    print("模拟8步游戏...")
    
    for step, (x, y, player) in enumerate(moves, 1):
        print(f"\n第{step}步: {'黑棋' if player == 1 else '白棋'}在({x},{y})落子")
        
        # 设置当前玩家
        game.current_player = player
        
        # 检查位置是否为空
        if game.board[y][x] != 0:
            print(f"错误：位置({x},{y})已有棋子!")
            continue
        
        # 放置棋子
        game.board[y][x] = player
        game.move_history.append((x, y, player))
        game.last_move = (x, y)
        
        print_board(game.board)
        
        # 检查胜负
        if game.check_win(x, y, player):
            winner = "黑棋" if player == 1 else "白棋"
            print(f"*** {winner}在第{step}步获胜！***")
            
            # 验证是否真的有五连
            has_five = False
            directions = [(1, 0), (0, 1), (1, 1), (1, -1)]
            
            for dx, dy in directions:
                count = 1
                
                # 正方向
                i, j = x + dx, y + dy
                while (0 <= i < 15 and 0 <= j < 15 and game.board[j][i] == player):
                    count += 1
                    i += dx
                    j += dy
                
                # 反方向
                i, j = x - dx, y - dy
                while (0 <= i < 15 and 0 <= j < 15 and game.board[j][i] == player):
                    count += 1
                    i -= dx
                    j -= dy
                
                if count >= 5:
                    has_five = True
                    print(f"✓ 确实有{count}连，获胜判断正确")
                    break
            
            if not has_five:
                print("✗ 错误！没有五连但判断为获胜！")
                return False
            
            return True
        else:
            print(f"✓ 第{step}步后没有获胜")
    
    print("\n✓ 8步游戏模拟完成，没有出现错误获胜判断")
    return True

def main():
    """主测试函数"""
    print("测试错误获胜判断问题")
    print("=" * 50)
    
    tests = [
        ("过早错误获胜判断", test_early_false_win),
        ("AI评估功能", test_ai_evaluation),
        ("手动游戏模拟", test_manual_game_simulation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 出错: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！错误获胜判断问题已修复！")
    else:
        print("⚠️  部分测试失败，仍有问题需要解决")

if __name__ == "__main__":
    main()
