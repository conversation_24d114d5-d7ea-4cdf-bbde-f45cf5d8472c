"""
最终测试：确认所有问题都已修复
"""

from gomoku_game import GomokuGame
import time

def print_board(board, title=""):
    """打印棋盘"""
    if title:
        print(f"{title}:")
    print("   ", end="")
    for i in range(15):
        print(f"{i:2}", end="")
    print()
    
    for i, row in enumerate(board):
        print(f"{i:2} ", end="")
        for cell in row:
            if cell == 0:
                print(" .", end="")
            elif cell == 1:
                print(" ●", end="")
            else:
                print(" ○", end="")
        print()
    print()

def test_complete_game_scenario():
    """测试完整的游戏场景"""
    print("=== 完整游戏场景测试 ===")
    
    game = GomokuGame()
    
    # 模拟一个完整的游戏过程
    moves = [
        (7, 7, 1),   # 黑棋中心
        (8, 8, 2),   # 白棋对角
        (6, 6, 1),   # 黑棋
        (9, 9, 2),   # 白棋
        (8, 7, 1),   # 黑棋
        (7, 8, 2),   # 白棋
        (9, 8, 1),   # 黑棋
        (6, 7, 2),   # 白棋
        (5, 5, 1),   # 黑棋
        (10, 10, 2), # 白棋
        (10, 9, 1),  # 黑棋
        (5, 9, 2),   # 白棋
        (11, 10, 1), # 黑棋
        (4, 8, 2),   # 白棋
        (12, 11, 1), # 黑棋
    ]
    
    print("模拟15步游戏，确保没有错误获胜判断...")
    
    for step, (x, y, player) in enumerate(moves, 1):
        print(f"\n第{step}步: {'黑棋' if player == 1 else '白棋'}在({x},{y})落子")
        
        # 设置当前玩家
        game.current_player = player
        
        # 检查位置是否为空
        if game.board[y][x] != 0:
            print(f"错误：位置({x},{y})已有棋子!")
            continue
        
        # 放置棋子
        game.board[y][x] = player
        game.move_history.append((x, y, player))
        game.last_move = (x, y)
        
        # 检查胜负
        if game.check_win(x, y, player):
            winner = "黑棋" if player == 1 else "白棋"
            print(f"*** {winner}在第{step}步获胜！***")
            
            # 验证是否真的有五连
            has_five = verify_five_in_row(game.board, x, y, player)
            
            if has_five:
                print("✓ 确实有五连，获胜判断正确")
                print_board(game.board, "最终棋盘")
                return True
            else:
                print("✗ 错误！没有五连但判断为获胜！")
                print_board(game.board, "错误的棋盘")
                return False
        else:
            print(f"✓ 第{step}步后没有获胜，正确")
    
    print("\n✓ 15步游戏完成，没有出现错误获胜判断")
    print_board(game.board, "最终棋盘")
    return True

def verify_five_in_row(board, x, y, player):
    """验证是否真的有五连"""
    directions = [(1, 0), (0, 1), (1, 1), (1, -1)]
    
    for dx, dy in directions:
        count = 1
        
        # 正方向
        i, j = x + dx, y + dy
        while (0 <= i < 15 and 0 <= j < 15 and board[j][i] == player):
            count += 1
            i += dx
            j += dy
        
        # 反方向
        i, j = x - dx, y - dy
        while (0 <= i < 15 and 0 <= j < 15 and board[j][i] == player):
            count += 1
            i -= dx
            j -= dy
        
        if count >= 5:
            print(f"找到{count}连在方向({dx},{dy})")
            return True
    
    return False

def test_ai_stability():
    """测试AI稳定性"""
    print("=== AI稳定性测试 ===")
    
    game = GomokuGame()
    
    # 设置一个中等复杂度的局面
    setup_moves = [
        (7, 7, 1),   # 黑棋
        (8, 8, 2),   # 白棋
        (6, 6, 1),   # 黑棋
        (9, 9, 2),   # 白棋
        (8, 7, 1),   # 黑棋
    ]
    
    for x, y, player in setup_moves:
        game.board[y][x] = player
    
    print_board(game.board, "测试局面")
    
    # 测试AI多次计算是否稳定
    print("测试AI连续5次计算...")
    
    for i in range(5):
        print(f"第{i+1}次计算...")
        start_time = time.time()
        
        try:
            x, y = game.ai.get_best_move(game.board, 2)
            end_time = time.time()
            
            print(f"  AI选择: ({x}, {y}), 用时: {end_time - start_time:.3f}秒")
            
            # 检查位置是否有效
            if x is None or y is None or not (0 <= x < 15 and 0 <= y < 15):
                print("  ✗ AI返回无效位置")
                return False
            
            if game.board[y][x] != 0:
                print(f"  ✗ AI选择了已有棋子的位置，值为: {game.board[y][x]}")
                print_board(game.board, "当前棋盘状态")
                return False
            
            print("  ✓ AI选择有效")
            
        except Exception as e:
            print(f"  ✗ AI计算出错: {e}")
            return False
    
    print("✓ AI稳定性测试通过")
    return True

def test_win_condition_accuracy():
    """测试胜负条件准确性"""
    print("=== 胜负条件准确性测试 ===")
    
    game = GomokuGame()
    
    # 测试真正的五连
    print("测试真正的五连...")
    for i in range(5):
        game.board[7][5+i] = 1
    
    print_board(game.board, "五连棋盘")
    
    # 检查是否正确识别为获胜
    if game.check_win(7, 7, 1):
        print("✓ 正确识别五连获胜")
    else:
        print("✗ 未能识别五连获胜")
        return False
    
    # 测试四连（不应该获胜）
    game.board = [[0 for _ in range(15)] for _ in range(15)]
    print("\n测试四连（不应该获胜）...")
    for i in range(4):
        game.board[7][5+i] = 1
    
    print_board(game.board, "四连棋盘")
    
    # 检查是否正确识别为未获胜
    if not game.check_win(7, 7, 1):
        print("✓ 正确识别四连未获胜")
    else:
        print("✗ 错误识别四连为获胜")
        return False
    
    print("✓ 胜负条件准确性测试通过")
    return True

def main():
    """主测试函数"""
    print("五子棋游戏最终测试")
    print("=" * 50)
    print("确认所有问题都已修复...")
    print()
    
    tests = [
        ("胜负条件准确性", test_win_condition_accuracy),
        ("AI稳定性", test_ai_stability),
        ("完整游戏场景", test_complete_game_scenario),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"{test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过\n")
            else:
                print(f"✗ {test_name} 失败\n")
        except Exception as e:
            print(f"✗ {test_name} 出错: {e}\n")
            import traceback
            traceback.print_exc()
    
    print("=" * 50)
    print(f"最终测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有问题已修复！游戏可以正常使用！")
        print("\n游戏特点:")
        print("• 胜负判断准确，只有真正的五连才算获胜")
        print("• AI智能稳定，不会选择无效位置")
        print("• 搜索算法优化，减少超时问题")
        print("• 默认难度适中，提供良好的游戏体验")
    else:
        print("⚠️  仍有问题需要解决")

if __name__ == "__main__":
    main()
